# Compiled class file
*.class

# Log file
*.log

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# Maven
target/
!.mvn/wrapper/maven-wrapper.jar

# IDE specific files
.idea/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
.vscode/
*.launch

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment variables
.env

# Logs
logs/
*.log

# Local development files
application-dev.yml

# Build directory
build/

# Database files
*.db
*.h2.db
*.sqlite

# Node modules (for frontend if any)
node_modules/

# Frontend build directories (for future frontend integration)
dist/
build/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development files
.idea/
*.iml
.project
.classpath
.settings/

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Docker
*.pem
*.p12
*.key
*.crt

# Flyway (if using)
/flyway/

# Application properties for local development
application-local.yml

# Log files
*.log.*

# Temp files
*.tmp
*.bak
*.swp
*~.nib

# Local development environment
.idea/
*.iml
.project
.classpath
.settings/

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Docker Compose
.docker-sync/

# Local development environment files
!.env.example

# Application logs
logs/

# Testing reports and logs
/test-output/
/test-reports/
/target/surefire-reports/
/target/site/

# Maven wrapper (if not using the wrapper)
.mvn/wrapper/maven-wrapper.jar

# Application specific
*.orig
*.log.*
*.log.*.gz
