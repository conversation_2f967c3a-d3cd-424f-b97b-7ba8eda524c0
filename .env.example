# Database Configuration
POSTGRES_DB=reelsnack
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# JWT Configuration
JWT_SECRET=your-256-bit-secret-key-32-characters-long
JWT_EXPIRATION_MS=86400000  # 24 hours in milliseconds

# Application Configuration
SPRING_PROFILES_ACTIVE=dev
SERVER_PORT=8080

# PGAdmin Configuration (optional)
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin

# Email Configuration (for future use)
# SPRING_MAIL_HOST=smtp.example.com
# SPRING_MAIL_PORT=587
# SPRING_MAIL_USERNAME=<EMAIL>
# SPRING_MAIL_PASSWORD=your-email-password
# SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH=true
# SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE=true

# AWS Configuration (for future use)
# AWS_ACCESS_KEY_ID=your-access-key-id
# AWS_SECRET_ACCESS_KEY=your-secret-access-key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET_NAME=your-s3-bucket

# Redis Configuration (for future use)
# SPRING_REDIS_HOST=redis
# SPRING_REDIS_PORT=6379
# SPRING_REDIS_PASSWORD=your-redis-password
