# ============================================
# ReelSnack Application Configuration
# ============================================

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always
  connection-timeout: 300000  # 5 minutes for large uploads
  tomcat:
    max-http-form-post-size: 100MB
    max-swallow-size: 100MB
    max-http-post-size: 100MB

# Application Properties
app:
  # Media Configuration
  media:
    max-file-size: 100MB
    storage:
      base-dir: ${user.home}/.reelsnack/uploads
      temp-dir: ${java.io.tmpdir}/reelsnack-uploads

    # Video Processing
    video:
      enabled: true
      processing-timeout: 300  # 5 minutes in seconds
      max-resolution: 3840x2160  # 4K UHD
      thumbnail:
        width: 320
        height: 180  # 16:9 aspect ratio

    # Allowed MIME Types
    allowed-types:
      - image/jpeg
      - image/png
      - image/gif
      - video/mp4
      - video/quicktime
      - video/x-msvideo
      - video/x-ms-wmv
      - video/x-flv
      - video/webm

  # JWT Configuration
  jwt:
    secret: ${JWT_SECRET:your-256-bit-secret-key-change-this-in-production}
    expiration-ms: ${JWT_EXPIRATION_MS:86400000}  # 24 hours
    issuer: ${JWT_ISSUER:ReelSnackAPI}
    audience: ${JWT_AUDIENCE:reelsnack-users}
    header: ${JWT_HEADER:Authorization}
    prefix: ${JWT_PREFIX:Bearer}

  # Email Configuration
  email:
    sender: ${MAIL_SENDER:<EMAIL>}

  # Spring Mail Configuration
  mail:
    host: ${SPRING_MAIL_HOST:localhost}
    port: ${SPRING_MAIL_PORT:1025}  # MailHog default
    username: ${SPRING_MAIL_USERNAME:}
    password: ${SPRING_MAIL_PASSWORD:}
    protocol: smtp
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false

  # Frontend Configuration
  frontend:
    url: ${FRONTEND_URL:http://localhost:3000}
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      exposed-headers: Authorization,Content-Disposition
      allow-credentials: true
      max-age: 3600

# Spring Configuration
spring:
  application:
    name: reelsnack

  # HTTP Configuration
  http:
    log-request-details: true

  # File Upload Configuration
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2MB
      resolve-lazily: true
      location: ${app.media.storage.temp-dir}

  # Async Configuration
  mvc:
    async:
      request-timeout: 30s
    servlet:
      multipart:
        max-in-memory-size: 1MB

  # Database Configuration
  datasource:
    url: ${SPRING_DATASOURCE_URL:******************************************}
    username: ${SPRING_DATASOURCE_USERNAME:postgres}
    password: ${SPRING_DATASOURCE_PASSWORD:901008401@}
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 1200000

  # JPA/Hibernate Configuration
  jpa:
    hibernate:
      ddl-auto: validate  # Changed from 'update' to 'validate' to avoid DDL conflicts
    show-sql: false  # Reduced logging for cleaner startup
    open-in-view: false
    properties:
      hibernate:
        format_sql: true
        # dialect: org.hibernate.dialect.PostgreSQLDialect  # Removed - auto-detected
        jdbc:
          batch_size: 20
          fetch_size: 50
          lob:
            non_contextual_creation: true
        order_inserts: true
        order_updates: true
        batch_versioned_data: true

  # Flyway Configuration
  flyway:
    enabled: true  # Re-enabled with proper configuration
    clean-on-validation-error: false  # More conservative
    baseline-on-migrate: true
    validate-on-migrate: false  # Temporarily disable validation to fix checksum
    validate-migration-naming: false  # Temporarily disable naming validation
    repair-on-migrate: true  # Enable auto-repair to fix checksum mismatch
    clean-disabled: true  # Disable clean for safety
    locations: classpath:db/migration
    baseline-version: 1
    baseline-description: Baseline Migration
    mixed: true  # Allow mixed transactional and non-transactional statements

  # Redis Configuration
  redis:
    host: localhost
    port: 6379
    timeout: 5000  # 5 seconds
    ssl: false

  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 3600000  # 1 hour
      cache-null-values: false
      key-prefix: "reelsnack:"
      use-key-prefix: true

  # Task Execution (Async)
  task:
    execution:
      thread-name-prefix: task-executor-
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
        allow-core-thread-timeout: true
        keep-alive: 60s
    scheduling:
      thread-name-prefix: task-scheduler-
      pool:
        size: 10

# Logging Configuration
logging:
  level:
    root: INFO
    com.reelsnack: DEBUG
    com.reelsnack.reelsnack.security: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: INFO
  file:
    name: logs/application.log
    max-size: 10MB
    max-history: 7
    total-size-cap: 100MB
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Actuator Configuration
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
    shutdown:
      enabled: true
  metrics:
    enable:
      http:
        server:
          requests: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
  server:
    port: 8081
    servlet:
      context-path: /management
    ssl:
      enabled: false
  health:
    db:
      enabled: true
    diskSpace:
      enabled: true
    redis:
      enabled: true
  info:
    env:
      enabled: true

# ============================================
# Environment-Specific Overrides
# ============================================
---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev

  # Development-specific settings
  h2:
    console:
      enabled: true
      path: /h2-console
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # Enable more verbose logging for development
logging:
  level:
    root: INFO
    com.reelsnack: DEBUG
    org.hibernate: DEBUG
    org.springframework: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod

  # Production-specific settings
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: false

  # More conservative logging in production
  logging:
    level:
      root: INFO
      com.reelsnack: INFO
      org.hibernate: WARN
      org.springframework: INFO

  # Production security settings
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:}
            client-secret: ${GOOGLE_CLIENT_SECRET:}
          facebook:
            client-id: ${FACEBOOK_CLIENT_ID:}
            client-secret: ${FACEBOOK_CLIENT_SECRET:}
          github:
            client-id: ${GITHUB_CLIENT_ID:}
            client-secret: ${GITHUB_CLIENT_SECRET:}
