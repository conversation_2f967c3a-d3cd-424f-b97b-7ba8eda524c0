-- V11__create_user_follows_system.sql
-- Create user following system

-- Create user_follows table for following relationships
CREATE TABLE IF NOT EXISTS user_follows (
    id BIGSERIAL PRIMARY KEY,
    follower_id BIGINT NOT NULL,
    following_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (follower_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users (id) ON DELETE CASCADE,
    
    -- Prevent self-following and duplicate follows
    CONSTRAINT no_self_follow CHECK (follower_id != following_id),
    CONSTRAINT unique_follow UNIQUE (follower_id, following_id)
);

-- Add verification field to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_verified BOOLEAN NOT NULL DEFAULT FALSE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_follows_follower ON user_follows (follower_id);
CREATE INDEX IF NOT EXISTS idx_user_follows_following ON user_follows (following_id);
CREATE INDEX IF NOT EXISTS idx_user_follows_created_at ON user_follows (created_at DESC);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_user_follows_follower_created ON user_follows (follower_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_follows_following_created ON user_follows (following_id, created_at DESC);

-- Add index for verification status
CREATE INDEX IF NOT EXISTS idx_users_verified ON users (is_verified) WHERE is_verified = TRUE;

-- Add comments for documentation
COMMENT ON TABLE user_follows IS 'User following relationships';
COMMENT ON COLUMN user_follows.follower_id IS 'User who is following';
COMMENT ON COLUMN user_follows.following_id IS 'User being followed';
COMMENT ON COLUMN user_follows.created_at IS 'When the follow relationship was created';

COMMENT ON COLUMN users.is_verified IS 'Whether the user is verified (blue checkmark)';

-- Create function to get follower count
CREATE OR REPLACE FUNCTION get_follower_count(user_id BIGINT)
RETURNS BIGINT AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM user_follows WHERE following_id = user_id);
END;
$$ LANGUAGE plpgsql;

-- Create function to get following count
CREATE OR REPLACE FUNCTION get_following_count(user_id BIGINT)
RETURNS BIGINT AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM user_follows WHERE follower_id = user_id);
END;
$$ LANGUAGE plpgsql;

-- Create function to check if user A follows user B
CREATE OR REPLACE FUNCTION is_following(follower_id BIGINT, following_id BIGINT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_follows 
        WHERE user_follows.follower_id = is_following.follower_id 
        AND user_follows.following_id = is_following.following_id
    );
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent following yourself
CREATE OR REPLACE FUNCTION prevent_self_follow()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.follower_id = NEW.following_id THEN
        RAISE EXCEPTION 'Users cannot follow themselves';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_prevent_self_follow
    BEFORE INSERT OR UPDATE ON user_follows
    FOR EACH ROW
    EXECUTE FUNCTION prevent_self_follow();

-- Create materialized view for user stats (optional, for performance)
CREATE MATERIALIZED VIEW IF NOT EXISTS user_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COALESCE(follower_counts.follower_count, 0) as follower_count,
    COALESCE(following_counts.following_count, 0) as following_count,
    COALESCE(content_counts.content_count, 0) as content_count
FROM users u
LEFT JOIN (
    SELECT following_id, COUNT(*) as follower_count
    FROM user_follows
    GROUP BY following_id
) follower_counts ON u.id = follower_counts.following_id
LEFT JOIN (
    SELECT follower_id, COUNT(*) as following_count
    FROM user_follows
    GROUP BY follower_id
) following_counts ON u.id = following_counts.follower_id
LEFT JOIN (
    SELECT creator_id, COUNT(*) as content_count
    FROM contents
    WHERE is_public = TRUE AND is_deleted = FALSE
    GROUP BY creator_id
) content_counts ON u.id = content_counts.creator_id;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_stats_user_id ON user_stats (user_id);

-- Function to refresh user stats
CREATE OR REPLACE FUNCTION refresh_user_stats()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_stats;
END;
$$ LANGUAGE plpgsql;
