-- V10__create_comments_system.sql
-- Create comments system for content engagement

-- Create comments table
CREATE TABLE IF NOT EXISTS comments (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    parent_comment_id BIGINT NULL, -- For nested replies
    comment_text TEXT NOT NULL,
    is_edited BOOLEAN NOT NULL DEFAULT FALSE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    like_count BIGINT NOT NULL DEFAULT 0,
    reply_count BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (content_id) REFERENCES contents (id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON>EY (parent_comment_id) <PERSON><PERSON>ERENCES comments (id) ON DELETE CASCADE
);

-- <PERSON>reate comment likes junction table
CREATE TABLE IF NOT EXISTS comment_likes (
    comment_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (comment_id, user_id),
    FOREIGN KEY (comment_id) REFERENCES comments (id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_comments_content_id ON comments (content_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments (user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments (parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_content_created ON comments (content_id, created_at DESC);

-- Create indexes for comment likes
CREATE INDEX IF NOT EXISTS idx_comment_likes_comment_id ON comment_likes (comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_likes_user_id ON comment_likes (user_id);

-- Add comments for documentation
COMMENT ON TABLE comments IS 'User comments on content with support for nested replies';
COMMENT ON COLUMN comments.content_id IS 'Reference to the content being commented on';
COMMENT ON COLUMN comments.user_id IS 'Reference to the user who made the comment';
COMMENT ON COLUMN comments.parent_comment_id IS 'Reference to parent comment for nested replies (NULL for top-level comments)';
COMMENT ON COLUMN comments.comment_text IS 'The actual comment text content';
COMMENT ON COLUMN comments.is_edited IS 'Whether the comment has been edited after creation';
COMMENT ON COLUMN comments.is_deleted IS 'Soft delete flag for comments';
COMMENT ON COLUMN comments.like_count IS 'Denormalized count of likes on this comment';
COMMENT ON COLUMN comments.reply_count IS 'Denormalized count of replies to this comment';

COMMENT ON TABLE comment_likes IS 'Junction table for users liking comments';

-- Create trigger to update comment count on content when comments are added/removed
CREATE OR REPLACE FUNCTION update_content_comment_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment comment count
        UPDATE contents 
        SET comment_count = comment_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.content_id;
        
        -- If this is a reply, increment parent comment reply count
        IF NEW.parent_comment_id IS NOT NULL THEN
            UPDATE comments 
            SET reply_count = reply_count + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = NEW.parent_comment_id;
        END IF;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement comment count
        UPDATE contents 
        SET comment_count = comment_count - 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = OLD.content_id;
        
        -- If this was a reply, decrement parent comment reply count
        IF OLD.parent_comment_id IS NOT NULL THEN
            UPDATE comments 
            SET reply_count = reply_count - 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = OLD.parent_comment_id;
        END IF;
        
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for comment count updates
CREATE TRIGGER trigger_update_content_comment_count
    AFTER INSERT OR DELETE ON comments
    FOR EACH ROW
    EXECUTE FUNCTION update_content_comment_count();

-- Create trigger to update comment like count
CREATE OR REPLACE FUNCTION update_comment_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE comments 
        SET like_count = like_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.comment_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE comments 
        SET like_count = like_count - 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = OLD.comment_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for comment like count updates
CREATE TRIGGER trigger_update_comment_like_count
    AFTER INSERT OR DELETE ON comment_likes
    FOR EACH ROW
    EXECUTE FUNCTION update_comment_like_count();
