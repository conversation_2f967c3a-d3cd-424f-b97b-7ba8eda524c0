-- =============================================
-- V9: Add indexes for content table performance
-- =============================================
-- Note: Using regular CREATE INDEX for faster startup
-- In production, consider using CONCURRENTLY for zero-downtime

-- Create indexes for performance (without CONCURRENTLY for faster startup)
CREATE INDEX IF NOT EXISTS idx_contents_duration ON contents(duration_seconds)
    WHERE duration_seconds IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_contents_dimensions ON contents(width_px, height_px)
    WHERE width_px IS NOT NULL AND height_px IS NOT NULL;

-- Index for common filtering patterns
CREATE INDEX IF NOT EXISTS idx_contents_public_created ON contents(created_at DESC)
    WHERE is_public = true;

-- Add a partial index for video content
CREATE INDEX IF NOT EXISTS idx_contents_video_metadata ON contents(
    content_type,
    duration_seconds,
    width_px,
    height_px
) WHERE content_type = 'VIDEO';

-- Analyze the table to update statistics
ANALYZE contents;
