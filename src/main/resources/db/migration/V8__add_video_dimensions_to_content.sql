-- =============================================
-- V8: Add video dimensions to content table
-- =============================================

-- Add new columns with constraints
ALTER TABLE contents
    ADD COLUMN IF NOT EXISTS width_px INTEGER
        CONSTRAINT positive_width CHECK (width_px IS NULL OR width_px > 0),
    ADD COLUMN IF NOT EXISTS height_px INTEGER
        CONSTRAINT positive_height CHECK (height_px IS NULL OR height_px > 0),
    ADD COLUMN IF NOT EXISTS duration_seconds INTEGER
        CONSTRAINT positive_duration CHECK (duration_seconds IS NULL OR duration_seconds > 0),
    ADD COLUMN IF NOT EXISTS aspect_ratio DECIMAL(5,2)
        GENERATED ALWAYS AS (CASE
            WHEN width_px IS NOT NULL AND height_px IS NOT NULL AND height_px > 0
            THEN ROUND(width_px::DECIMAL / height_px, 2)
            ELSE NULL
        END) STORED;

-- Add comments for documentation
COMMENT ON COLUMN contents.width_px IS 'Width of the video in pixels (for video content)';
COMMENT ON COLUMN contents.height_px IS 'Height of the video in pixels (for video content)';
COMMENT ON COLUMN contents.duration_seconds IS 'Duration of the video in seconds (for video content)';
COMMENT ON COLUMN contents.aspect_ratio IS 'Aspect ratio (width/height) for video content, calculated automatically';

-- Add monitoring and maintenance functions
CREATE OR REPLACE FUNCTION update_content_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update denormalized metrics (example)
    -- This could be used to maintain summary tables or materialized views
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
