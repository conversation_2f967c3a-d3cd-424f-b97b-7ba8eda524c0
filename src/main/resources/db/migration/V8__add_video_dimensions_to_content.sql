-- =============================================
-- V8: Add video dimensions to content table
-- =============================================

-- Add new columns with constraints
ALTER TABLE contents
    ADD COLUMN IF NOT EXISTS width_px INTEGER
        CONSTRAINT positive_width CHECK (width_px IS NULL OR width_px > 0),
    ADD COLUMN IF NOT EXISTS height_px INTEGER
        CONSTRAINT positive_height CHECK (height_px IS NULL OR height_px > 0),
    ADD COLUMN IF NOT EXISTS duration_seconds INTEGER
        CONSTRAINT positive_duration CHECK (duration_seconds IS NULL OR duration_seconds > 0),
    ADD COLUMN IF NOT EXISTS aspect_ratio DECIMAL(5,2)
        GENERATED ALWAYS AS (CASE
            WHEN width_px IS NOT NULL AND height_px IS NOT NULL AND height_px > 0
            THEN ROUND(width_px::DECIMAL / height_px, 2)
            ELSE NULL
        END) STORED;

-- Add comments for documentation
COMMENT ON COLUMN contents.width_px IS 'Width of the video in pixels (for video content)';
COMMENT ON COLUMN contents.height_px IS 'Height of the video in pixels (for video content)';
COMMENT ON COLUMN contents.duration_seconds IS 'Duration of the video in seconds (for video content)';
COMMENT ON COLUMN contents.aspect_ratio IS 'Aspect ratio (width/height) for video content, calculated automatically';

-- Update existing video content metadata in background (if needed)
-- This would be a good candidate for a separate background job in production
-- DO $$
-- BEGIN
--     PERFORM pg_sleep(1); -- Prevent lock contention
--     -- Update existing video content with metadata extraction
--     -- This is a placeholder - actual implementation would use a background job
--     RAISE NOTICE 'Consider running background job to update existing video metadata';
-- END $$;

-- Add table partitioning for large datasets (example for PostgreSQL 10+)
-- Uncomment and customize if your content table is expected to be very large
-- CREATE TABLE contents_new (LIKE contents INCLUDING INDEXES) PARTITION BY RANGE (created_at);
-- ALTER TABLE contents RENAME TO contents_old;
-- ALTER TABLE contents_new RENAME TO contents;
-- -- Create monthly partitions
-- CREATE TABLE contents_y2023m07 PARTITION OF contents
--     FOR VALUES FROM ('2023-07-01') TO ('2023-08-01');
-- -- Add more partitions as needed

-- Create a rollback script in a separate file (V8.1__rollback_video_dimensions.sql)
-- This would contain the necessary DROP statements to revert the changes

-- Add monitoring and maintenance functions
CREATE OR REPLACE FUNCTION update_content_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update denormalized metrics (example)
    -- This could be used to maintain summary tables or materialized views
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for data consistency
-- DROP TRIGGER IF EXISTS trg_content_metrics ON contents;
-- CREATE TRIGGER trg_content_metrics
--     AFTER INSERT OR UPDATE OF width_px, height_px, duration_seconds
--     ON contents
--     FOR EACH ROW
--     EXECUTE FUNCTION update_content_metrics();

-- Commit the transaction
COMMIT;

-- Analyze the table to update statistics
ANALYZE contents;

-- =============================================
-- V8.1__rollback_video_dimensions.sql
-- =============================================
-- This would be a separate file for rollback purposes
/*
-- Start transaction
BEGIN;

-- Drop triggers first
DROP TRIGGER IF EXISTS trg_content_metrics ON contents;

-- Drop function
DROP FUNCTION IF EXISTS update_content_metrics();

-- Drop indexes
DROP INDEX IF EXISTS idx_contents_duration;
DROP INDEX IF EXISTS idx_contents_dimensions;
DROP INDEX IF EXISTS idx_contents_public_created;
DROP INDEX IF EXISTS idx_contents_video_metadata;

-- Drop columns
ALTER TABLE contents 
    DROP COLUMN IF EXISTS width_px,
    DROP COLUMN IF EXISTS height_px,
    DROP COLUMN IF EXISTS duration_seconds,
    DROP COLUMN IF EXISTS aspect_ratio;

-- Drop constraints (PostgreSQL doesn't support IF EXISTS for constraints in ALTER TABLE)
-- You may need to handle this in application code or with dynamic SQL

COMMIT;
*/
