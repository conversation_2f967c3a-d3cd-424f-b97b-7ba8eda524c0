package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for user follow statistics.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "User follow statistics")
public class FollowStatsResponse {

    @Schema(description = "User ID", example = "123")
    private Long userId;

    @Schema(description = "Username", example = "chef_maria")
    private String username;

    @Schema(description = "Number of followers", example = "1250")
    private Long followerCount;

    @Schema(description = "Number of users being followed", example = "180")
    private Long followingCount;

    @Schema(description = "Number of content items created", example = "45")
    private Long contentCount;

    @Schema(description = "Whether the current user follows this user", example = "true")
    private Boolean isFollowedByCurrentUser;

    @Schema(description = "Whether this user follows the current user back", example = "false")
    private Boolean followsCurrentUser;

    @Schema(description = "Number of mutual followers", example = "25")
    private Long mutualFollowersCount;
}
