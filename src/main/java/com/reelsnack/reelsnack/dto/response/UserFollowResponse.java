package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO for user follow relationships.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "User follow relationship information")
public class UserFollowResponse {

    @Schema(description = "Follow relationship ID", example = "123")
    private Long id;

    @Schema(description = "Information about the follower")
    private UserSummaryResponse follower;

    @Schema(description = "Information about the user being followed")
    private UserSummaryResponse following;

    @Schema(description = "When the follow relationship was created", example = "2023-12-01T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * Simplified user information for follow responses.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Summary information about a user in follow context")
    public static class UserSummaryResponse {

        @Schema(description = "User ID", example = "123")
        private Long id;

        @Schema(description = "Username", example = "chef_maria")
        private String username;

        @Schema(description = "User's first name", example = "Maria")
        private String firstName;

        @Schema(description = "User's last name", example = "Rodriguez")
        private String lastName;

        @Schema(description = "User's profile picture URL", example = "https://example.com/profile.jpg")
        private String profilePictureUrl;

        @Schema(description = "User's bio/description", example = "Professional chef and food blogger")
        private String bio;

        @Schema(description = "Whether this user is verified", example = "true")
        private Boolean isVerified;

        @Schema(description = "User's role in the platform", example = "CHEF")
        private String role;

        @Schema(description = "Number of followers", example = "1250")
        private Long followerCount;

        @Schema(description = "Number of users being followed", example = "180")
        private Long followingCount;

        @Schema(description = "Number of content items created", example = "45")
        private Long contentCount;

        @Schema(description = "Whether the current user follows this user", example = "true")
        private Boolean isFollowedByCurrentUser;
    }
}
