package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Response DTO for comment information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Comment information")
public class CommentResponse {

    @Schema(description = "Comment ID", example = "123")
    private Long id;

    @Schema(description = "Content ID this comment belongs to", example = "456")
    private Long contentId;

    @Schema(description = "Parent comment ID if this is a reply", example = "789")
    private Long parentCommentId;

    @Schema(description = "The comment text content", example = "This recipe looks amazing!")
    private String commentText;

    @Schema(description = "Whether the comment has been edited", example = "false")
    private Boolean isEdited;

    @Schema(description = "Whether the comment has been deleted", example = "false")
    private Boolean isDeleted;

    @Schema(description = "Number of likes on this comment", example = "15")
    private Long likeCount;

    @Schema(description = "Number of replies to this comment", example = "3")
    private Long replyCount;

    @Schema(description = "Whether the current user has liked this comment", example = "true")
    private Boolean isLikedByCurrentUser;

    @Schema(description = "Information about the user who made the comment")
    private CommentUserResponse user;

    @Schema(description = "When the comment was created", example = "2023-12-01T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "When the comment was last updated", example = "2023-12-01T10:35:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Simplified user information for comment responses.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "User information in comment context")
    public static class CommentUserResponse {

        @Schema(description = "User ID", example = "123")
        private Long id;

        @Schema(description = "Username", example = "chef_maria")
        private String username;

        @Schema(description = "User's first name", example = "Maria")
        private String firstName;

        @Schema(description = "User's last name", example = "Rodriguez")
        private String lastName;

        @Schema(description = "User's profile picture URL", example = "https://example.com/profile.jpg")
        private String profilePictureUrl;

        @Schema(description = "Whether this user is verified", example = "true")
        private Boolean isVerified;

        @Schema(description = "User's role in the platform", example = "CHEF")
        private String role;
    }
}
