package com.reelsnack.reelsnack.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for comment data.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Comment information with user details and engagement metrics")
public class CommentResponse {

    @Schema(description = "Unique identifier of the comment", example = "123")
    private Long id;

    @Schema(description = "ID of the content this comment belongs to", example = "456")
    private Long contentId;

    @Schema(description = "ID of the parent comment if this is a reply", example = "789")
    private Long parentCommentId;

    @Schema(description = "The comment text content", example = "This recipe looks amazing!")
    private String commentText;

    @Schema(description = "Information about the user who made the comment")
    private UserSummaryResponse user;

    @Schema(description = "Whether the comment has been edited", example = "false")
    private Boolean isEdited;

    @Schema(description = "Whether the comment has been deleted", example = "false")
    private Boolean isDeleted;

    @Schema(description = "Number of likes on this comment", example = "25")
    private Long likeCount;

    @Schema(description = "Number of replies to this comment", example = "3")
    private Long replyCount;

    @Schema(description = "Whether the current user has liked this comment", example = "true")
    private Boolean isLikedByCurrentUser;

    @Schema(description = "Whether the current user can edit this comment", example = "true")
    private Boolean canEdit;

    @Schema(description = "Whether the current user can delete this comment", example = "true")
    private Boolean canDelete;

    @Schema(description = "List of replies to this comment (for nested comments)")
    private List<CommentResponse> replies;

    @Schema(description = "When the comment was created", example = "2023-12-01T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "When the comment was last updated", example = "2023-12-01T11:15:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * Simplified user information for comment responses.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Summary information about the comment author")
    public static class UserSummaryResponse {

        @Schema(description = "User ID", example = "123")
        private Long id;

        @Schema(description = "Username", example = "chef_maria")
        private String username;

        @Schema(description = "User's first name", example = "Maria")
        private String firstName;

        @Schema(description = "User's last name", example = "Rodriguez")
        private String lastName;

        @Schema(description = "User's profile picture URL", example = "https://example.com/profile.jpg")
        private String profilePictureUrl;

        @Schema(description = "User's bio/description", example = "Professional chef and food blogger")
        private String bio;

        @Schema(description = "Whether this user is verified", example = "true")
        private Boolean isVerified;

        @Schema(description = "User's role in the platform", example = "CHEF")
        private String role;
    }
}
