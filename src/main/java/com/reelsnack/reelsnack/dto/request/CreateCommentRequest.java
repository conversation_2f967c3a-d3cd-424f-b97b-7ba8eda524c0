package com.reelsnack.reelsnack.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for creating a new comment on content.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to create a new comment")
public class CreateCommentRequest {

    @NotBlank(message = "Comment text is required")
    @Size(max = 2000, message = "Comment text cannot exceed 2000 characters")
    @Schema(
        description = "The comment text content",
        example = "This recipe looks amazing! Can't wait to try it.",
        maxLength = 2000,
        required = true
    )
    private String commentText;

    @Schema(
        description = "ID of the parent comment if this is a reply (optional for top-level comments)",
        example = "123"
    )
    private Long parentCommentId;
}
