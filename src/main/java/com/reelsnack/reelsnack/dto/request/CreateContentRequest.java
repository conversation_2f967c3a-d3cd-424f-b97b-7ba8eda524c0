package com.reelsnack.reelsnack.dto.request;

import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.ServiceCategory;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.Set;

@Data
public class CreateContentRequest {
    @NotBlank(message = "Title is required")
    private String title;
    
    private String description;
    
    @NotNull(message = "Content type is required")
    private ContentType contentType;
    
    private Set<String> categories;
    
    private Boolean isPublic = true;
    
    private Boolean isPromoted = false;
}
