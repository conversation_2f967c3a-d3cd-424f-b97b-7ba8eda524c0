package com.reelsnack.reelsnack.dto.request;

import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.ServiceCategory;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Set;

/**
 * DTO for updating existing content
 */
@Data
@Schema(description = "Request object for updating content")
public class UpdateContentRequest {

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    @Schema(description = "Title of the content", example = "How to make perfect pasta")
    private String title;

    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    @Schema(description = "Detailed description of the content", 
           example = "Step by step guide to making perfect pasta at home")
    private String description;

    @Schema(description = "Type of content (RECIPE_VIDEO, TUTORIAL, etc.)")
    private ContentType contentType;

    @NotNull(message = "Categories cannot be null")
    @Size(min = 1, message = "At least one category is required")
    @ArraySchema(schema = @Schema(implementation = String.class),
               arraySchema = @Schema(description = "List of categories this content belongs to",
                                   example = "[\"COOKING_CLASS\", \"MEAL_DELIVERY\"]",
                                   requiredMode = Schema.RequiredMode.NOT_REQUIRED))
    private Set<String> categories;

    @Schema(description = "List of tags for better discoverability", 
           example = "[\"pasta\", \"italian\", \"dinner\"]")
    private Set<String> tags;

    @Schema(description = "Whether the content is public or private", 
           example = "true",
           defaultValue = "true",
           requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean isPublic = true;

    @Schema(description = "Whether the content is promoted/featured", 
           example = "false",
           defaultValue = "false",
           requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean isPromoted = false;

    @Schema(description = "Duration of the content in seconds (auto-calculated if not provided)", 
           example = "300",
           requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer duration;

    @Schema(description = "Thumbnail URL (auto-generated if not provided)", 
           example = "https://example.com/thumbnails/123.jpg",
           requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String thumbnailUrl;
}
