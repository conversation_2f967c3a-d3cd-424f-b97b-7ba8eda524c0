package com.reelsnack.reelsnack.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for updating an existing comment.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to update an existing comment")
public class UpdateCommentRequest {

    @NotBlank(message = "Comment text is required")
    @Size(max = 2000, message = "Comment text cannot exceed 2000 characters")
    @Schema(
        description = "The updated comment text content",
        example = "This recipe looks amazing! Can't wait to try it. UPDATE: Just tried it and it's delicious!",
        maxLength = 2000,
        required = true
    )
    private String commentText;
}
