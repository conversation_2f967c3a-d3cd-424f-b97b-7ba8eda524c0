package com.reelsnack.reelsnack.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Represents a comment on content with support for nested replies.
 * Comments can be liked by users and can have replies (nested comments).
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "comments", indexes = {
    @Index(name = "idx_comments_content_id", columnList = "content_id"),
    @Index(name = "idx_comments_user_id", columnList = "user_id"),
    @Index(name = "idx_comments_parent_id", columnList = "parent_comment_id"),
    @Index(name = "idx_comments_created_at", columnList = "created_at DESC"),
    @Index(name = "idx_comments_content_created", columnList = "content_id, created_at DESC")
})
public class Comment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Content is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "content_id", nullable = false, foreignKey = @ForeignKey(name = "fk_comment_content"))
    private Content content;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "fk_comment_user"))
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_comment_id", foreignKey = @ForeignKey(name = "fk_comment_parent"))
    private Comment parentComment;

    @OneToMany(mappedBy = "parentComment", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Comment> replies = new ArrayList<>();

    @NotBlank(message = "Comment text is required")
    @Size(max = 2000, message = "Comment text cannot exceed 2000 characters")
    @Column(name = "comment_text", nullable = false, length = 2000)
    private String commentText;

    @Column(name = "is_edited", nullable = false)
    @Builder.Default
    private Boolean isEdited = false;

    @Column(name = "is_deleted", nullable = false)
    @Builder.Default
    private Boolean isDeleted = false;

    @Column(name = "like_count", nullable = false)
    @Builder.Default
    private Long likeCount = 0L;

    @Column(name = "reply_count", nullable = false)
    @Builder.Default
    private Long replyCount = 0L;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "comment_likes",
        joinColumns = @JoinColumn(name = "comment_id"),
        inverseJoinColumns = @JoinColumn(name = "user_id"),
        foreignKey = @ForeignKey(name = "fk_comment_likes_comment"),
        inverseForeignKey = @ForeignKey(name = "fk_comment_likes_user")
    )
    @JsonIgnore
    @Builder.Default
    private Set<User> likedBy = new HashSet<>();

    // ===== Business Methods =====

    /**
     * Checks if this comment is a top-level comment (not a reply).
     * @return true if this is a top-level comment, false if it's a reply
     */
    public boolean isTopLevel() {
        return parentComment == null;
    }

    /**
     * Checks if this comment is a reply to another comment.
     * @return true if this is a reply, false if it's a top-level comment
     */
    public boolean isReply() {
        return parentComment != null;
    }

    /**
     * Adds a reply to this comment.
     * @param reply The reply comment to add
     */
    public void addReply(Comment reply) {
        if (reply != null) {
            replies.add(reply);
            reply.setParentComment(this);
        }
    }

    /**
     * Removes a reply from this comment.
     * @param reply The reply comment to remove
     */
    public void removeReply(Comment reply) {
        if (reply != null) {
            replies.remove(reply);
            reply.setParentComment(null);
        }
    }

    /**
     * Increments the like count for this comment.
     */
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0L : this.likeCount) + 1;
    }

    /**
     * Decrements the like count for this comment.
     */
    public void decrementLikeCount() {
        this.likeCount = Math.max(0L, (this.likeCount == null ? 0L : this.likeCount) - 1);
    }

    /**
     * Increments the reply count for this comment.
     */
    public void incrementReplyCount() {
        this.replyCount = (this.replyCount == null ? 0L : this.replyCount) + 1;
    }

    /**
     * Decrements the reply count for this comment.
     */
    public void decrementReplyCount() {
        this.replyCount = Math.max(0L, (this.replyCount == null ? 0L : this.replyCount) - 1);
    }

    /**
     * Marks this comment as edited.
     */
    public void markAsEdited() {
        this.isEdited = true;
    }

    /**
     * Soft deletes this comment by marking it as deleted.
     */
    public void softDelete() {
        this.isDeleted = true;
        this.commentText = "[Comment deleted]";
    }

    /**
     * Checks if the comment is visible (not deleted).
     * @return true if the comment is visible, false if deleted
     */
    public boolean isVisible() {
        return !Boolean.TRUE.equals(isDeleted);
    }

    // ===== Helper Methods for JSON Serialization =====

    /**
     * Gets the content ID for JSON serialization.
     * @return the content ID
     */
    public Long getContentId() {
        return content != null ? content.getId() : null;
    }

    /**
     * Gets the parent comment ID for JSON serialization.
     * @return the parent comment ID or null if this is a top-level comment
     */
    public Long getParentCommentId() {
        return parentComment != null ? parentComment.getId() : null;
    }

    /**
     * Gets the user ID for JSON serialization.
     * @return the user ID
     */
    public Long getUserId() {
        return user != null ? user.getId() : null;
    }
}
