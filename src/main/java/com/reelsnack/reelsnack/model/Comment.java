package com.reelsnack.reelsnack.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Represents a comment on content with support for nested replies.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "comments",
    indexes = {
        @Index(name = "idx_comments_content", columnList = "content_id"),
        @Index(name = "idx_comments_user", columnList = "user_id"),
        @Index(name = "idx_comments_parent", columnList = "parent_comment_id"),
        @Index(name = "idx_comments_created_at", columnList = "created_at DESC"),
        @Index(name = "idx_comments_content_created", columnList = "content_id, created_at DESC")
    }
)
public class Comment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Content is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "content_id", nullable = false, foreignKey = @ForeignKey(name = "fk_comment_content"))
    @JsonIgnore
    private Content content;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, foreignKey = @ForeignKey(name = "fk_comment_user"))
    @JsonIgnore
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_comment_id", foreignKey = @ForeignKey(name = "fk_comment_parent"))
    @JsonIgnore
    private Comment parentComment;

    @NotNull(message = "Comment text is required")
    @Size(max = 2000, message = "Comment text cannot exceed 2000 characters")
    @Column(name = "comment_text", nullable = false, length = 2000)
    private String commentText;

    @Column(name = "is_edited", nullable = false)
    @Builder.Default
    private Boolean isEdited = false;

    @Column(name = "is_deleted", nullable = false)
    @Builder.Default
    private Boolean isDeleted = false;

    @Column(name = "like_count", nullable = false)
    @Builder.Default
    private Long likeCount = 0L;

    @Column(name = "reply_count", nullable = false)
    @Builder.Default
    private Long replyCount = 0L;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "comment_likes",
        joinColumns = @JoinColumn(name = "comment_id"),
        inverseJoinColumns = @JoinColumn(name = "user_id"),
        foreignKey = @ForeignKey(name = "fk_comment_likes_comment"),
        inverseForeignKey = @ForeignKey(name = "fk_comment_likes_user")
    )
    @JsonIgnore
    @Builder.Default
    private Set<User> likedBy = new HashSet<>();

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // ===== Business Methods =====

    /**
     * Marks the comment as edited.
     */
    public void markAsEdited() {
        this.isEdited = true;
    }

    /**
     * Soft deletes the comment.
     */
    public void softDelete() {
        this.isDeleted = true;
        this.commentText = "[Comment deleted]";
    }

    /**
     * Increments the like count.
     */
    public void incrementLikeCount() {
        this.likeCount++;
    }

    /**
     * Decrements the like count.
     */
    public void decrementLikeCount() {
        if (this.likeCount > 0) {
            this.likeCount--;
        }
    }

    /**
     * Gets the content ID for convenience.
     */
    public Long getContentId() {
        return content != null ? content.getId() : null;
    }

    /**
     * Gets the parent comment ID for convenience.
     */
    public Long getParentCommentId() {
        return parentComment != null ? parentComment.getId() : null;
    }

    /**
     * Gets the user ID for convenience.
     */
    public Long getUserId() {
        return user != null ? user.getId() : null;
    }
}
