package com.reelsnack.reelsnack.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Represents a following relationship between two users.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_follows", 
    uniqueConstraints = {
        @UniqueConstraint(name = "unique_follow", columnNames = {"follower_id", "following_id"})
    },
    indexes = {
        @Index(name = "idx_user_follows_follower", columnList = "follower_id"),
        @Index(name = "idx_user_follows_following", columnList = "following_id"),
        @Index(name = "idx_user_follows_created_at", columnList = "created_at DESC")
    }
)
public class UserFollow {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Follower is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "follower_id", nullable = false, foreignKey = @ForeignKey(name = "fk_follow_follower"))
    private User follower;

    @NotNull(message = "Following user is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "following_id", nullable = false, foreignKey = @ForeignKey(name = "fk_follow_following"))
    private User following;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // ===== Business Methods =====

    /**
     * Checks if this is a valid follow relationship (not self-follow).
     */
    public boolean isValidFollow() {
        return follower != null && following != null && 
               !follower.getId().equals(following.getId());
    }

    /**
     * Gets the follower's ID for convenience.
     */
    public Long getFollowerId() {
        return follower != null ? follower.getId() : null;
    }

    /**
     * Gets the following user's ID for convenience.
     */
    public Long getFollowingId() {
        return following != null ? following.getId() : null;
    }

    /**
     * Gets the follower's username for convenience.
     */
    public String getFollowerUsername() {
        return follower != null ? follower.getUsername() : null;
    }

    /**
     * Gets the following user's username for convenience.
     */
    public String getFollowingUsername() {
        return following != null ? following.getUsername() : null;
    }

    // ===== Validation Methods =====

    @PrePersist
    @PreUpdate
    private void validateFollow() {
        if (!isValidFollow()) {
            throw new IllegalArgumentException("Users cannot follow themselves");
        }
    }
}
