package com.reelsnack.reelsnack.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.ServiceCategory;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.SQLRestriction;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * Represents a piece of content in the system, which can be a recipe, tutorial, or other media.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "contents", indexes = {
    @Index(name = "idx_content_creator", columnList = "creator_id"),
    @Index(name = "idx_content_public", columnList = "is_public"),
    @Index(name = "idx_content_promoted", columnList = "is_promoted"),
    @Index(name = "idx_content_created_at", columnList = "created_at DESC")
})
@SQLRestriction("is_deleted = false")
public class Content {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    @Column(nullable = false, length = 200)
    private String title;

    @Size(max = 5000, message = "Description cannot exceed 5000 characters")
    @Column(columnDefinition = "TEXT")
    private String description;

    @NotBlank(message = "Media URL is required")
    @Column(name = "media_url", nullable = false, length = 512)
    private String mediaUrl;

    @Column(name = "thumbnail_url", length = 512)
    private String thumbnailUrl;

    @NotNull(message = "Content type is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "content_type", nullable = false, length = 20)
    private ContentType contentType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false, foreignKey = @ForeignKey(name = "fk_content_creator"))
    private User creator;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "content_categories",
        joinColumns = @JoinColumn(name = "content_id"),
        foreignKey = @ForeignKey(name = "fk_content_categories_content")
    )
    @Enumerated(EnumType.STRING)
    @Column(name = "category", length = 50)
    private Set<ServiceCategory> categories = new HashSet<>();

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "content_tags",
        joinColumns = @JoinColumn(name = "content_id"),
        foreignKey = @ForeignKey(name = "fk_content_tags_content")
    )
    @Column(name = "tag", length = 50)
    private Set<@Size(max = 50) String> tags = new HashSet<>();

    @Column(name = "duration_seconds")
    private Integer duration; // in seconds

    @Column(name = "width_px")
    private Integer width; // in pixels

    @Column(name = "height_px")
    private Integer height; // in pixels
    
    @Column(name = "aspect_ratio", insertable = false, updatable = false)
    private BigDecimal aspectRatio; // width/height, calculated in database

    @Column(name = "view_count", nullable = false)
    @Builder.Default
    private Long viewCount = 0L;

    @Column(name = "like_count", nullable = false)
    @Builder.Default
    private Long likeCount = 0L;

    @Column(name = "share_count", nullable = false)
    @Builder.Default
    private Long shareCount = 0L;

    @Builder.Default
    @Column(name = "comment_count", nullable = false)
    private Long commentCount = 0L;

    // Explicit getters and setters for boolean fields to maintain compatibility
    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "is_public", nullable = false)
    @Builder.Default
    private Boolean isPublic = true;

    @Column(name = "is_promoted", nullable = false)
    @Builder.Default
    private Boolean isPromoted = false;

    @Column(name = "is_deleted", nullable = false)
    @Builder.Default
    private Boolean isDeleted = false;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "content_likes",
        joinColumns = @JoinColumn(name = "content_id"),
        inverseJoinColumns = @JoinColumn(name = "user_id"),
        foreignKey = @ForeignKey(name = "fk_content_likes_content"),
        inverseForeignKey = @ForeignKey(name = "fk_content_likes_user")
    )
    @JsonIgnore
    private Set<User> likedBy = new HashSet<>();

    @OneToMany(mappedBy = "content", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonIgnore
    @Builder.Default
    private List<Comment> comments = new ArrayList<>();

    /**
     * Adds a category to the content.
     * @param category The category to add
     * @return true if the category was added, false if it already exists
     */
    public boolean addCategory(ServiceCategory category) {
        return categories.add(category);
    }

    /**
     * Removes a category from the content.
     * @param category The category to remove
     * @return true if the category was removed, false if it didn't exist
     */
    public boolean removeCategory(ServiceCategory category) {
        return categories.remove(category);
    }

    /**
     * Adds a tag to the content.
     * @param tag The tag to add
     * @return true if the tag was added, false if it already exists
     */
    public boolean addTag(String tag) {
        return tags.add(tag.trim().toLowerCase());
    }

    /**
     * Removes a tag from the content.
     * @param tag The tag to remove
     * @return true if the tag was removed, false if it didn't exist
     */
    public boolean removeTag(String tag) {
        return tags.remove(tag.trim().toLowerCase());
    }

    /**
     * Checks if the content has a specific tag.
     * @param tag The tag to check
     * @return true if the tag exists, false otherwise
     */
    public boolean hasTag(String tag) {
        return tags.contains(tag.trim().toLowerCase());
    }

    /**
     * Increments the view count by 1.
     */
    public void incrementViewCount() {
        this.viewCount++;
    }

    /**
     * Increments the like count by 1.
     */
    public void incrementLikeCount() {
        this.likeCount++;
    }

    /**
     * Decrements the like count by 1, but not below 0.
     */
    public void decrementLikeCount() {
        this.likeCount = Math.max(0, this.likeCount - 1);
    }

    @Builder
    public Content(Long id, String title, String description, String mediaUrl, String thumbnailUrl, 
                  ContentType contentType, User creator, Set<ServiceCategory> categories, Set<String> tags,
                  boolean isPublic, boolean isPromoted, Integer duration, Integer width, Integer height) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.mediaUrl = mediaUrl;
        this.thumbnailUrl = thumbnailUrl;
        this.contentType = contentType;
        this.creator = creator;
        this.categories = categories != null ? categories : new HashSet<>();
        this.tags = tags != null ? tags : new HashSet<>();
        this.isPublic = isPublic;
        this.isPromoted = isPromoted;
        this.duration = duration;
        this.width = width;
        this.height = height;
        this.aspectRatio = (width != null && height != null && height > 0) ? 
            BigDecimal.valueOf((double) width / height).setScale(2, RoundingMode.HALF_UP) : null;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Helper method to calculate aspect ratio
     * @return The aspect ratio as a string (e.g., "16:9") or null if dimensions are not set
     */
    @Transient
    public String getAspectRatioDisplay() {
        if (width == null || height == null || height == 0) {
            return null;
        }
        
        // Simplify the ratio
        int gcd = findGCD(width, height);
        return String.format("%d:%d", width / gcd, height / gcd);
    }
    
    // Helper method to find greatest common divisor
    private int findGCD(int a, int b) {
        return b == 0 ? a : findGCD(b, a % b);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Content content = (Content) o;
        return id != null && id.equals(content.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @PrePersist
    protected void onCreate() {
        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now();
        }
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Soft deletes the content by marking it as deleted.
     */
    public void softDelete() {
        this.isDeleted = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Restores a soft-deleted content by marking it as not deleted.
     */
    public void restore() {
        this.isDeleted = false;
        this.updatedAt = LocalDateTime.now();
    }


}
