package com.reelsnack.reelsnack.model;

import com.reelsnack.reelsnack.model.enums.Role;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * Represents a user in the system.
 * Implements Spring Security's UserDetails for authentication and authorization.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"password", "emailVerificationToken", "passwordResetToken", "userPreference"}, callSuper = false)
@EqualsAndHashCode(exclude = {"password", "emailVerificationToken", "passwordResetToken", "userPreference"}, callSuper = false)
@Entity
@Table(name = "users", 
    uniqueConstraints = { 
        @UniqueConstraint(columnNames = "username"),
        @UniqueConstraint(columnNames = "email") 
    })
public class User implements UserDetails, Serializable {
    private static final long serialVersionUID = 1L;

    // ===== Entity Fields =====
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 20)
    private String username;

    @NotBlank
    @Size(max = 50)
    @Email
    private String email;

    @NotBlank
    @Size(max = 120)
    private String password;

    @Size(max = 15)
    @Builder.Default
    private String phoneNumber = "";

    @Size(max = 50)
    @Builder.Default
    private String firstName = "";

    @Size(max = 50)
    @Builder.Default
    private String lastName = "";

    @Column(name = "profile_picture")
    @Size(max = 255)
    @Builder.Default
    private String profilePicture = "";

    @Column(name = "enabled", nullable = false)
    @Builder.Default
    private boolean enabled = false;

    @Size(max = 500)
    @Builder.Default
    private String bio = "";

    @Column(name = "email_verification_token")
    private String emailVerificationToken;

    @Column(name = "email_verification_token_expiry")
    private LocalDateTime emailVerificationTokenExpiry;

    @Column(name = "password_reset_token")
    private String passwordResetToken;

    @Column(name = "password_reset_token_expiry")
    private LocalDateTime passwordResetTokenExpiry;

    @Column(name = "email_verified", nullable = false)
    @Builder.Default
    private boolean emailVerified = false;

    @OneToOne(mappedBy = "user", cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE}, 
              orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference
    private UserPreference userPreference;

    
    /**
     * Creates and sets a new user preference with default values.
     * 
     * @return The created user preference
     */
    public UserPreference createDefaultUserPreference() {
        UserPreference preference = UserPreference.builder()
                .theme("LIGHT")
                .notificationsEnabled(true)
                .language("en")
                .build();
        setUserPreference(preference);
        return preference;
    }

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private Role role;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    // ===== Constructors =====
    
    /**
     * Creates a new user with the given username, email, and password.
     * The user will be enabled by default.
     * 
     * @param username the username
     * @param email the email address
     * @param password the hashed password
     */
    public User(String username, String email, String password) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.enabled = true;
    }

    // ===== UserDetails Implementation =====
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return role != null ? List.of(new SimpleGrantedAuthority(role.name())) : List.of();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return this.enabled;
    }

    // ===== Custom Business Methods =====
    
    /**
     * Verifies the user's email address and clears the verification token.
     */
    @Transient
    public void verifyEmail() {
        this.emailVerified = true;
        this.emailVerificationToken = null;
        this.emailVerificationTokenExpiry = null;
    }

    /**
     * Sets the user preference and maintains the bidirectional relationship.
     * 
     * @param userPreference the user preference to set
     */
    public void setUserPreference(UserPreference userPreference) {
        if (this.userPreference == userPreference) {
            return;
        }
        
        // Unlink from the old user preference
        UserPreference oldPreference = this.userPreference;
        if (oldPreference != null) {
            oldPreference.setUser(null);
        }
        
        // Set the new user preference
        this.userPreference = userPreference;
        
        // Link the new user preference back to this user
        if (userPreference != null) {
            userPreference.setUser(this);
        }
    }
    
    // ===== Custom Getters/Setters =====
    
    @Transient
    public boolean isEmailVerified() {
        return emailVerified;
    }
    
    @Transient
    public String getPasswordResetToken() {
        return passwordResetToken;
    }
    
    @Transient
    public void setPasswordResetToken(String passwordResetToken) {
        this.passwordResetToken = passwordResetToken;
    }
    
    @Transient
    public LocalDateTime getPasswordResetTokenExpiry() {
        return passwordResetTokenExpiry;
    }
    
    @Transient
    public void setPasswordResetTokenExpiry(LocalDateTime passwordResetTokenExpiry) {
        this.passwordResetTokenExpiry = passwordResetTokenExpiry;
    }
    
    @Transient
    public String getEmailVerificationToken() {
        return emailVerificationToken;
    }
    
    @Transient
    public void setEmailVerificationToken(String emailVerificationToken) {
        this.emailVerificationToken = emailVerificationToken;
    }
    
    @Transient
    public LocalDateTime getEmailVerificationTokenExpiry() {
        return emailVerificationTokenExpiry;
    }
    
    @Transient
    public void setEmailVerificationTokenExpiry(LocalDateTime emailVerificationTokenExpiry) {
        this.emailVerificationTokenExpiry = emailVerificationTokenExpiry;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return Objects.equals(id, user.id) &&
               Objects.equals(username, user.username) &&
               Objects.equals(email, user.email) &&
               Objects.equals(phoneNumber, user.phoneNumber) &&
               Objects.equals(firstName, user.firstName) &&
               Objects.equals(lastName, user.lastName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, username, email, phoneNumber, firstName, lastName);
    }
    
    @Override
    public String toString() {
        return "User{" +
               "id=" + id +
               ", username='" + username + '\'' +
               ", email='" + email + '\'' +
               ", firstName='" + firstName + '\'' +
               ", lastName='" + lastName + '\'' +
               ", phoneNumber='" + phoneNumber + '\'' +
               ", role=" + role +
               ", enabled=" + enabled +
               '}';
    }
}
