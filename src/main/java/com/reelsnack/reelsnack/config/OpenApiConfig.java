package com.reelsnack.reelsnack.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Configuration for OpenAPI (Swagger) documentation.
 */
@Configuration
public class OpenApiConfig {

    private static final String SECURITY_SCHEME_NAME = "bearerAuth";
    
    @Value("${app.api.base-url:http://localhost:8080}")
    private String baseUrl;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME))
            .components(
                new Components()
                    .addSecuritySchemes(SECURITY_SCHEME_NAME,
                        new SecurityScheme()
                            .name(SECURITY_SCHEME_NAME)
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT")
                            .description("JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"")
                    )
                    // Add reusable schemas
                    .addSchemas("ErrorResponse", new Schema<Map<String, Object>>()
                        .addProperty("status", new StringSchema().example("error"))
                        .addProperty("message", new StringSchema().example("An error occurred"))
                        .addProperty("timestamp", new StringSchema().example("2023-01-01T12:00:00Z"))
                    )
            )
            .info(new Info()
                .title("ReelSnack API")
                .description("""
                    # ReelSnack API Documentation
                    
                    Welcome to the ReelSnack API documentation. This API provides endpoints for:
                    - User authentication and profile management
                    - Content management (videos, tutorials, recipes)
                    - Social features (likes, comments, shares)
                    
                    ## Authentication
                    Most endpoints require authentication using a JWT token. 
                    Include the token in the `Authorization` header as `Bearer <token>`.
                    
                    ## Rate Limiting
                    The API is rate limited to protect against abuse. 
                    - Public endpoints: 100 requests per minute per IP
                    - Authenticated endpoints: 1000 requests per minute per user
                    
                    ## Error Handling
                    Errors are returned in the following format:
                    ```json
                    {
                        "status": "error",
                        "message": "Error description",
                        "timestamp": "2023-01-01T12:00:00Z"
                    }
                    ```
                    """)
                .version("1.0.0")
                .contact(new Contact()
                    .name("ReelSnack Support")
                    .email("<EMAIL>")
                    .url("https://reelsnack.com/support")
                )
                .license(new License()
                    .name("Apache 2.0")
                    .url("https://www.apache.org/licenses/LICENSE-2.0.html")
                )
                .termsOfService("https://reelsnack.com/terms")
            )
            .servers(List.of(
                new Server()
                    .url(baseUrl)
                    .description("Current server")
            ));
    }
    
    /**
     * Customize the OpenAPI documentation with common parameters and headers
     */
    @Bean
    public OpenApiCustomizer openApiCustomizer() {
        return openApi -> {
            // Add common parameters to all operations
            openApi.getPaths().values().forEach(pathItem -> 
                pathItem.readOperations().forEach(operation -> {
                    // Add pagination parameters to all GET operations
                    if (operation.getOperationId() != null && 
                        (operation.getOperationId().contains("get") || 
                         operation.getOperationId().contains("list"))) {
                        
                        operation.addParametersItem(new Parameter()
                            .name("page")
                            .in("query")
                            .description("Page number (0-based)")
                            .required(false)
                            .schema(new Schema<Integer>()
                                .type("integer")
                                .minimum(java.math.BigDecimal.ZERO)
                                ._default(Integer.valueOf(0)))
                        );
                        
                        operation.addParametersItem(new Parameter()
                            .name("size")
                            .in("query")
                            .description("Number of items per page")
                            .required(false)
                            .schema(new Schema<Integer>()
                                .type("integer")
                                .minimum(java.math.BigDecimal.ONE)
                                .maximum(new java.math.BigDecimal(100))
                                ._default(Integer.valueOf(20)))
                        );
                        
                        operation.addParametersItem(new Parameter()
                            .name("sort")
                            .in("query")
                            .description("Sorting criteria in the format: property,(asc|desc). " +
                                       "Default sort order is ascending. " +
                                       "Multiple sort criteria are supported.")
                            .required(false)
                            .schema(new Schema<String>().type("string"))
                        );
                    }
                })
            );
            
            // Add security requirements to operations that need authentication
            openApi.getPaths().forEach((path, pathItem) ->
                pathItem.readOperations().forEach(operation -> {
                    // Skip adding security for public endpoints
                    boolean isPublicEndpoint =
                        (operation.getTags() != null &&
                         (operation.getTags().contains("Authentication") ||
                          operation.getTags().contains("Public"))) ||
                        isPublicContentEndpoint(path, operation);

                    if (!isPublicEndpoint) {
                        operation.addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME));
                    }
                })
            );
        };
    }

    /**
     * Check if a content endpoint should be public (no authentication required)
     */
    private boolean isPublicContentEndpoint(String path, Operation operation) {
        // Public endpoints that don't require authentication
        return path.equals("/api/v1/content") ||
               path.matches("/api/v1/content/\\{id\\}") ||
               path.matches("/api/v1/content/user/\\{username\\}") ||
               path.startsWith("/api/v1/content/search") ||
               path.equals("/api/v1/content/trending") ||
               path.matches("/api/v1/content/\\{id\\}/likes/count") ||
               path.equals("/api/v1/test/public") ||
               path.startsWith("/api/v1/auth/") ||
               path.startsWith("/v3/api-docs") ||
               path.startsWith("/swagger-ui") ||
               path.equals("/");
    }
}
