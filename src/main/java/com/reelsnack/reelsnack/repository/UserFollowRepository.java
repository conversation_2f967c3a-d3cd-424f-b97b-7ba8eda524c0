package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserFollow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for UserFollow entity operations.
 */
@Repository
public interface UserFollowRepository extends JpaRepository<UserFollow, Long> {

    // ===== Basic Follow Operations =====

    /**
     * Check if a user follows another user.
     * @param follower The user who might be following
     * @param following The user who might be followed
     * @return true if the follow relationship exists
     */
    boolean existsByFollowerAndFollowing(User follower, User following);

    /**
     * Find a specific follow relationship.
     * @param follower The follower user
     * @param following The following user
     * @return Optional UserFollow if relationship exists
     */
    Optional<UserFollow> findByFollowerAndFollowing(User follower, User following);

    /**
     * Delete a follow relationship.
     * @param follower The follower user
     * @param following The following user
     */
    void deleteByFollowerAndFollowing(User follower, User following);

    // ===== Follower Queries =====

    /**
     * Get all followers of a user.
     * @param following The user whose followers to get
     * @param pageable Pagination information
     * @return Page of UserFollow relationships
     */
    @Query("SELECT uf FROM UserFollow uf " +
           "LEFT JOIN FETCH uf.follower " +
           "WHERE uf.following = :following " +
           "ORDER BY uf.createdAt DESC")
    Page<UserFollow> findFollowersByUser(@Param("following") User following, Pageable pageable);

    /**
     * Get all users that a user is following.
     * @param follower The user whose following list to get
     * @param pageable Pagination information
     * @return Page of UserFollow relationships
     */
    @Query("SELECT uf FROM UserFollow uf " +
           "LEFT JOIN FETCH uf.following " +
           "WHERE uf.follower = :follower " +
           "ORDER BY uf.createdAt DESC")
    Page<UserFollow> findFollowingByUser(@Param("follower") User follower, Pageable pageable);

    // ===== Count Queries =====

    /**
     * Count followers of a user.
     * @param following The user to count followers for
     * @return Number of followers
     */
    long countByFollowing(User following);

    /**
     * Count users that a user is following.
     * @param follower The user to count following for
     * @return Number of users being followed
     */
    long countByFollower(User follower);

    // ===== Advanced Queries =====

    /**
     * Get users followed by a specific user (for feed generation).
     * @param follower The user whose followed users to get
     * @return List of followed users
     */
    @Query("SELECT uf.following FROM UserFollow uf WHERE uf.follower = :follower")
    List<User> findFollowedUsers(@Param("follower") User follower);

    /**
     * Get mutual followers between two users.
     * @param user1 First user
     * @param user2 Second user
     * @param pageable Pagination information
     * @return Page of mutual followers
     */
    @Query("SELECT uf1.follower FROM UserFollow uf1 " +
           "WHERE uf1.following = :user1 " +
           "AND EXISTS (SELECT 1 FROM UserFollow uf2 " +
           "           WHERE uf2.following = :user2 " +
           "           AND uf2.follower = uf1.follower)")
    Page<User> findMutualFollowers(@Param("user1") User user1, @Param("user2") User user2, Pageable pageable);

    /**
     * Get suggested users to follow (users followed by people you follow).
     * @param user The user to get suggestions for
     * @param pageable Pagination information
     * @return Page of suggested users
     */
    @Query("SELECT DISTINCT uf2.following FROM UserFollow uf1 " +
           "JOIN UserFollow uf2 ON uf1.following = uf2.follower " +
           "WHERE uf1.follower = :user " +
           "AND uf2.following != :user " +
           "AND NOT EXISTS (SELECT 1 FROM UserFollow uf3 " +
           "               WHERE uf3.follower = :user " +
           "               AND uf3.following = uf2.following)")
    Page<User> findSuggestedUsers(@Param("user") User user, Pageable pageable);

    /**
     * Get recent followers (for notifications).
     * @param following The user whose recent followers to get
     * @param pageable Pagination information
     * @return Page of recent followers
     */
    @Query("SELECT uf FROM UserFollow uf " +
           "LEFT JOIN FETCH uf.follower " +
           "WHERE uf.following = :following " +
           "ORDER BY uf.createdAt DESC")
    Page<UserFollow> findRecentFollowers(@Param("following") User following, Pageable pageable);

    /**
     * Check if user A follows user B by usernames.
     * @param followerUsername Username of potential follower
     * @param followingUsername Username of potential following
     * @return true if follow relationship exists
     */
    @Query("SELECT CASE WHEN COUNT(uf) > 0 THEN true ELSE false END " +
           "FROM UserFollow uf " +
           "WHERE uf.follower.username = :followerUsername " +
           "AND uf.following.username = :followingUsername")
    boolean existsByFollowerUsernameAndFollowingUsername(
        @Param("followerUsername") String followerUsername,
        @Param("followingUsername") String followingUsername
    );

    /**
     * Get follower count by username.
     * @param username The username to count followers for
     * @return Number of followers
     */
    @Query("SELECT COUNT(uf) FROM UserFollow uf WHERE uf.following.username = :username")
    long countFollowersByUsername(@Param("username") String username);

    /**
     * Get following count by username.
     * @param username The username to count following for
     * @return Number of users being followed
     */
    @Query("SELECT COUNT(uf) FROM UserFollow uf WHERE uf.follower.username = :username")
    long countFollowingByUsername(@Param("username") String username);

    /**
     * Get top followed users (influencers).
     * @param pageable Pagination information
     * @return Page of most followed users
     */
    @Query("SELECT uf.following, COUNT(uf) as followerCount " +
           "FROM UserFollow uf " +
           "GROUP BY uf.following " +
           "ORDER BY COUNT(uf) DESC")
    Page<Object[]> findTopFollowedUsers(Pageable pageable);

    /**
     * Delete all follows for a user (when user is deleted).
     * @param user The user whose follows to delete
     */
    void deleteByFollowerOrFollowing(User user, User user2);
}
