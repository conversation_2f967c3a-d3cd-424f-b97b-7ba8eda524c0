package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.ServiceCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ContentRepository extends JpaRepository<Content, Long> {
    // Basic CRUD operations are provided by JpaRepository
    
    // Find by creator with optional public filter
    Page<Content> findByCreator(User creator, Pageable pageable);

    
    // Find by content type with optional public filter
    Page<Content> findByContentType(ContentType contentType, Pageable pageable);
    Page<Content> findByContentTypeAndIsPublic(ContentType contentType, boolean isPublic, Pageable pageable);
    Page<Content> findByContentTypeAndIsPublicTrue(ContentType contentType, Pageable pageable);
    
    // Find by public status
    Page<Content> findByIsPublicTrue(Pageable pageable);
    
    // Find by creator and public status
    Page<Content> findByCreatorAndIsPublic(User creator, boolean isPublic, Pageable pageable);
    
    @Query("SELECT c FROM Content c WHERE c.creator.username = :username AND c.contentType = :contentType AND c.isPublic = true")
    Page<Content> findByUsernameAndContentTypeAndIsPublic(
            @Param("username") String username,
            @Param("contentType") ContentType contentType,
            Pageable pageable
    );
    
    // Find content by content type and category
    @Query("SELECT c FROM Content c JOIN c.categories cat WHERE c.contentType = :contentType AND cat = :category AND c.isPublic = true")
    Page<Content> findByContentTypeAndCategoriesContainingAndIsPublicTrue(
            @Param("contentType") ContentType contentType, 
            @Param("category") String category, 
            Pageable pageable);
    
    // Find by creator and content type with optional public filter
    Page<Content> findByCreatorAndContentType(User creator, ContentType contentType, Pageable pageable);
    Page<Content> findByCreatorAndContentTypeAndIsPublic(User creator, ContentType contentType, boolean isPublic, Pageable pageable);
    
    // Search functionality
    Page<Content> findByTitleContainingIgnoreCase(String query, Pageable pageable);
    Page<Content> findByTitleContainingIgnoreCaseAndIsPublic(String query, boolean isPublic, Pageable pageable);
    
    @Query("SELECT c FROM Content c WHERE LOWER(c.title) LIKE LOWER(concat('%', :query, '%')) " +
           "AND c.isPublic = :isPublic AND c.contentType = :contentType")
    Page<Content> searchByTitleAndTypeAndIsPublic(
            @Param("query") String query,
            @Param("contentType") ContentType contentType,
            @Param("isPublic") boolean isPublic,
            Pageable pageable
    );
    
    Page<Content> findByDescriptionContainingIgnoreCase(String query, Pageable pageable);
    Page<Content> findByDescriptionContainingIgnoreCaseAndIsPublic(String query, boolean isPublic, Pageable pageable);
    
    // Search by tags
    Page<Content> findByTagsIn(Set<String> tags, Pageable pageable);
    Page<Content> findByTagsInAndIsPublic(Set<String> tags, boolean isPublic, Pageable pageable);
    
    // Search by categories
    Page<Content> findByCategoriesIn(Set<String> categories, Pageable pageable);
    Page<Content> findByCategoriesInAndIsPublic(Set<String> categories, boolean isPublic, Pageable pageable);
    
    // Find by multiple categories (all must match)
    @Query("SELECT c FROM Content c JOIN c.categories cat WHERE cat IN :categories AND c.isPublic = true GROUP BY c HAVING COUNT(DISTINCT cat) = :categoryCount")
    Page<Content> findByCategoriesAndIsPublic(
            @Param("categories") Set<String> categories,
            @Param("categoryCount") long categoryCount,
            Pageable pageable);
    
    // Find by tags with public filter
    @Query("SELECT DISTINCT c FROM Content c JOIN c.tags t WHERE t IN :tags AND c.isPublic = true")
    Page<Content> findByTagsInAndIsPublicTrue(
            @Param("tags") List<String> tags, 
            Pageable pageable);
    
    // Find trending content (combination of views, likes, and recency)
    @Query(value = "SELECT c.* FROM contents c WHERE c.is_public = true " +
           "AND c.created_at >= (CURRENT_DATE - INTERVAL '7 days') " +
           "ORDER BY (c.view_count * 0.4 + c.like_count * 0.6) DESC",
           countQuery = "SELECT COUNT(*) FROM contents c WHERE c.is_public = true " +
                       "AND c.created_at >= (CURRENT_DATE - INTERVAL '7 days')",
           nativeQuery = true)
    Page<Content> findTrendingContent(Pageable pageable);
    
    // User feed with followed users' content
    @Query("SELECT c FROM Content c WHERE c.creator IN :creators AND c.isPublic = true ORDER BY c.createdAt DESC")
    Page<Content> findFeedByFollowedUsers(@Param("creators") List<User> creators, Pageable pageable);
    
    // Find promoted content
    Page<Content> findByIsPromotedAndIsPublic(boolean isPromoted, boolean isPublic, Pageable pageable);
    
    // Count likes for a content
    @Query("SELECT COUNT(l) FROM Content c JOIN c.likedBy l WHERE c.id = :contentId")
    long countLikesById(@Param("contentId") Long contentId);
    
    // Check if a user has liked a content
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM Content c JOIN c.likedBy u WHERE c.id = :contentId AND u.username = :username")
    boolean existsByIdAndLikedByUsername(@Param("contentId") Long contentId, @Param("username") String username);

    // Count content by creator
    long countByCreatorAndIsPublicTrueAndIsDeletedFalse(User creator);
    
    // Find content by category
    @Query("SELECT c FROM Content c WHERE :category MEMBER OF c.categories AND c.isPublic = true")
    Page<Content> findByCategoriesContainingAndIsPublicTrue(@Param("category") ServiceCategory category, Pageable pageable);
    
    // Find content by multiple categories
    @Query("SELECT DISTINCT c FROM Content c JOIN c.categories cat WHERE cat IN :categories AND c.isPublic = true")
    Page<Content> findByCategoriesInAndIsPublicTrue(@Param("categories") Set<String> categories, Pageable pageable);
    
    // Find content by multiple tags
    @Query("SELECT DISTINCT c FROM Content c JOIN c.tags t WHERE t IN :tags AND c.isPublic = true")
    Page<Content> findByTagsInAndIsPublicTrue(@Param("tags") Set<String> tags, Pageable pageable);
    
    // Advanced search with multiple criteria
    @Query("SELECT DISTINCT c FROM Content c " +
           "LEFT JOIN c.categories cat " +
           "LEFT JOIN c.tags t " +
           "WHERE (c.isPublic = :isPublic OR :isPublic IS NULL) " +
           "AND (c.contentType = :contentType OR :contentType IS NULL) " +
           "AND (:categories IS NULL OR cat IN :categories) " +
           "AND (:tags IS NULL OR t IN :tags) " +
           "AND (LOWER(c.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "     LOWER(c.description) LIKE LOWER(CONCAT('%', :query, '%')) OR :query IS NULL) " +
           "GROUP BY c " +
           "ORDER BY c.createdAt DESC")
    Page<Content> searchByFilters(
            @Param("query") String query,
            @Param("contentType") ContentType contentType,
            @Param("categories") Set<String> categories,
            @Param("tags") Set<String> tags,
            @Param("isPublic") Boolean isPublic,
            Pageable pageable
    );
    
    // Find content by creator and optional filters
    @Query("SELECT DISTINCT c FROM Content c " +
           "LEFT JOIN c.categories cat " +
           "LEFT JOIN c.tags t " +
           "WHERE c.creator.username = :username " +
           "AND (c.isPublic = :isPublic OR :isPublic IS NULL) " +
           "AND (c.contentType = :contentType OR :contentType IS NULL) " +
           "AND (:categories IS NULL OR cat IN :categories) " +
           "AND (:tags IS NULL OR t IN :tags) " +
           "AND (LOWER(c.title) LIKE LOWER(CONCAT('%', :query, '%')) OR :query IS NULL) " +
           "GROUP BY c " +
           "ORDER BY c.createdAt DESC")
    Page<Content> findByCreatorWithFilters(
            @Param("username") String username,
            @Param("query") String query,
            @Param("contentType") ContentType contentType,
            @Param("categories") Set<String> categories,
            @Param("tags") Set<String> tags,
            @Param("isPublic") Boolean isPublic,
            Pageable pageable
    );
    
    // Find content by multiple creators (for user feed)
    @Query("SELECT c FROM Content c WHERE c.creator IN :creators AND c.isPublic = true ORDER BY c.createdAt DESC")
    Page<Content> findByCreatorsInAndIsPublicTrue(@Param("creators") List<User> creators, Pageable pageable);
    
    // Find content by multiple creators with filters (for user feed with filters)
    @Query("SELECT DISTINCT c FROM Content c " +
           "LEFT JOIN c.categories cat " +
           "LEFT JOIN c.tags t " +
           "WHERE c.creator IN :creators " +
           "AND c.isPublic = true " +
           "AND (c.contentType = :contentType OR :contentType IS NULL) " +
           "AND (:categories IS NULL OR cat IN :categories) " +
           "AND (:tags IS NULL OR t IN :tags) " +
           "AND (LOWER(c.title) LIKE LOWER(CONCAT('%', :query, '%')) OR :query IS NULL) " +
           "GROUP BY c " +
           "ORDER BY c.createdAt DESC")
    Page<Content> findByCreatorsInWithFilters(
            @Param("creators") List<User> creators,
            @Param("query") String query,
            @Param("contentType") ContentType contentType,
            @Param("categories") Set<String> categories,
            @Param("tags") Set<String> tags,
            Pageable pageable
    );
}
