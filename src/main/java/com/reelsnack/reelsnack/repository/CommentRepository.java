package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.Comment;
import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Comment entity operations.
 */
@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {

    // ===== Basic Queries =====

    /**
     * Find all top-level comments for a specific content (not replies).
     * @param content The content to find comments for
     * @param pageable Pagination information
     * @return Page of top-level comments
     */
    Page<Comment> findByContentAndParentCommentIsNullAndIsDeletedFalse(Content content, Pageable pageable);

    /**
     * Find all replies to a specific comment.
     * @param parentComment The parent comment to find replies for
     * @param pageable Pagination information
     * @return Page of reply comments
     */
    Page<Comment> findByParentCommentAndIsDeletedFalse(Comment parentComment, Pageable pageable);

    /**
     * Find all comments by a specific user.
     * @param user The user to find comments for
     * @param pageable Pagination information
     * @return Page of user's comments
     */
    Page<Comment> findByUserAndIsDeletedFalse(User user, Pageable pageable);

    /**
     * Find a comment by ID that is not deleted.
     * @param id The comment ID
     * @return Optional comment if found and not deleted
     */
    Optional<Comment> findByIdAndIsDeletedFalse(Long id);

    // ===== Advanced Queries =====

    /**
     * Find top-level comments for content with user and like information.
     * @param contentId The content ID
     * @param pageable Pagination information
     * @return Page of comments with user details
     */
    @Query("SELECT c FROM Comment c " +
           "LEFT JOIN FETCH c.user " +
           "WHERE c.content.id = :contentId " +
           "AND c.parentComment IS NULL " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findTopLevelCommentsWithUser(@Param("contentId") Long contentId, Pageable pageable);

    /**
     * Find replies to a comment with user information.
     * @param parentCommentId The parent comment ID
     * @param pageable Pagination information
     * @return Page of reply comments with user details
     */
    @Query("SELECT c FROM Comment c " +
           "LEFT JOIN FETCH c.user " +
           "WHERE c.parentComment.id = :parentCommentId " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt ASC")
    Page<Comment> findRepliesWithUser(@Param("parentCommentId") Long parentCommentId, Pageable pageable);

    /**
     * Count total comments for a content (including replies).
     * @param contentId The content ID
     * @return Total comment count
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.content.id = :contentId AND c.isDeleted = false")
    long countByContentId(@Param("contentId") Long contentId);

    /**
     * Count top-level comments for a content (excluding replies).
     * @param contentId The content ID
     * @return Top-level comment count
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.content.id = :contentId AND c.parentComment IS NULL AND c.isDeleted = false")
    long countTopLevelCommentsByContentId(@Param("contentId") Long contentId);

    /**
     * Count replies for a specific comment.
     * @param commentId The parent comment ID
     * @return Reply count
     */
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.parentComment.id = :commentId AND c.isDeleted = false")
    long countRepliesByCommentId(@Param("commentId") Long commentId);

    /**
     * Check if a user has liked a specific comment.
     * @param commentId The comment ID
     * @param username The username
     * @return true if user has liked the comment
     */
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END " +
           "FROM Comment c JOIN c.likedBy u " +
           "WHERE c.id = :commentId AND u.username = :username")
    boolean existsByIdAndLikedByUsername(@Param("commentId") Long commentId, @Param("username") String username);

    /**
     * Count likes for a specific comment.
     * @param commentId The comment ID
     * @return Like count
     */
    @Query("SELECT COUNT(u) FROM Comment c JOIN c.likedBy u WHERE c.id = :commentId")
    long countLikesById(@Param("commentId") Long commentId);

    /**
     * Find recent comments by a user across all content.
     * @param user The user
     * @param pageable Pagination information
     * @return Page of recent comments
     */
    @Query("SELECT c FROM Comment c " +
           "LEFT JOIN FETCH c.content " +
           "WHERE c.user = :user " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findRecentCommentsByUser(@Param("user") User user, Pageable pageable);

    /**
     * Find most liked comments for a content.
     * @param contentId The content ID
     * @param pageable Pagination information
     * @return Page of most liked comments
     */
    @Query("SELECT c FROM Comment c " +
           "LEFT JOIN FETCH c.user " +
           "WHERE c.content.id = :contentId " +
           "AND c.parentComment IS NULL " +
           "AND c.isDeleted = false " +
           "ORDER BY c.likeCount DESC, c.createdAt DESC")
    Page<Comment> findMostLikedComments(@Param("contentId") Long contentId, Pageable pageable);

    /**
     * Find comments that mention a specific user (for notifications).
     * @param username The username to search for
     * @param pageable Pagination information
     * @return Page of comments mentioning the user
     */
    @Query("SELECT c FROM Comment c " +
           "LEFT JOIN FETCH c.user " +
           "LEFT JOIN FETCH c.content " +
           "WHERE c.commentText LIKE CONCAT('%@', :username, '%') " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findCommentsMentioningUser(@Param("username") String username, Pageable pageable);

    /**
     * Delete all comments for a specific content (cascade delete).
     * @param content The content whose comments should be deleted
     */
    void deleteByContent(Content content);

    /**
     * Soft delete all comments by a user.
     * @param user The user whose comments should be soft deleted
     */
    @Query("UPDATE Comment c SET c.isDeleted = true, c.commentText = '[Comment deleted]' WHERE c.user = :user")
    void softDeleteByUser(@Param("user") User user);
}
