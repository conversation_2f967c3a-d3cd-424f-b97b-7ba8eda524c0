package com.reelsnack.reelsnack.repository;

import com.reelsnack.reelsnack.model.Comment;
import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for Comment entity operations.
 */
@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {

    // ===== Basic Comment Queries =====

    /**
     * Find all comments for a specific content item.
     * @param content The content to find comments for
     * @param pageable Pagination information
     * @return Page of comments
     */
    Page<Comment> findByContentAndIsDeletedFalseOrderByCreatedAtDesc(Content content, Pageable pageable);

    /**
     * Find top-level comments (no parent) for a content item.
     * @param content The content to find comments for
     * @param pageable Pagination information
     * @return Page of top-level comments
     */
    Page<Comment> findByContentAndParentCommentIsNullAndIsDeletedFalseOrderByCreatedAtDesc(Content content, Pageable pageable);

    /**
     * Find replies to a specific comment.
     * @param parentComment The parent comment
     * @param pageable Pagination information
     * @return Page of reply comments
     */
    Page<Comment> findByParentCommentAndIsDeletedFalseOrderByCreatedAtAsc(Comment parentComment, Pageable pageable);

    /**
     * Find all comments by a specific user.
     * @param user The user who made the comments
     * @param pageable Pagination information
     * @return Page of user's comments
     */
    Page<Comment> findByUserAndIsDeletedFalseOrderByCreatedAtDesc(User user, Pageable pageable);

    // ===== Count Queries =====

    /**
     * Count total comments for a content item (excluding deleted).
     * @param content The content to count comments for
     * @return Number of comments
     */
    long countByContentAndIsDeletedFalse(Content content);

    /**
     * Count top-level comments for a content item.
     * @param content The content to count comments for
     * @return Number of top-level comments
     */
    long countByContentAndParentCommentIsNullAndIsDeletedFalse(Content content);

    /**
     * Count replies to a specific comment.
     * @param parentComment The parent comment
     * @return Number of replies
     */
    long countByParentCommentAndIsDeletedFalse(Comment parentComment);

    // ===== Like Queries =====

    /**
     * Check if a user has liked a specific comment.
     * @param commentId The comment ID
     * @param username The username
     * @return true if user has liked the comment
     */
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END " +
           "FROM Comment c JOIN c.likedBy u " +
           "WHERE c.id = :commentId AND u.username = :username")
    boolean existsByIdAndLikedByUsername(@Param("commentId") Long commentId, @Param("username") String username);

    // ===== Advanced Queries =====

    /**
     * Find comments mentioning a specific user.
     * @param username The username to search for mentions
     * @param pageable Pagination information
     * @return Page of comments mentioning the user
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE c.commentText LIKE CONCAT('%@', :username, '%') " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findCommentsMentioningUser(@Param("username") String username, Pageable pageable);

    /**
     * Find recent comments for a content item (for notifications).
     * @param content The content
     * @param pageable Pagination information
     * @return Page of recent comments
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE c.content = :content " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findRecentCommentsByContent(@Param("content") Content content, Pageable pageable);

    /**
     * Find most liked comments for a content item.
     * @param content The content
     * @param pageable Pagination information
     * @return Page of most liked comments
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE c.content = :content " +
           "AND c.isDeleted = false " +
           "ORDER BY c.likeCount DESC, c.createdAt DESC")
    Page<Comment> findMostLikedCommentsByContent(@Param("content") Content content, Pageable pageable);

    /**
     * Find comments by content ID (for convenience).
     * @param contentId The content ID
     * @param pageable Pagination information
     * @return Page of comments
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE c.content.id = :contentId " +
           "AND c.parentComment IS NULL " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findTopLevelCommentsByContentId(@Param("contentId") Long contentId, Pageable pageable);

    /**
     * Find replies by parent comment ID.
     * @param parentCommentId The parent comment ID
     * @param pageable Pagination information
     * @return Page of reply comments
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE c.parentComment.id = :parentCommentId " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt ASC")
    Page<Comment> findRepliesByParentCommentId(@Param("parentCommentId") Long parentCommentId, Pageable pageable);

    /**
     * Search comments by text content.
     * @param searchText The text to search for
     * @param pageable Pagination information
     * @return Page of matching comments
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE LOWER(c.commentText) LIKE LOWER(CONCAT('%', :searchText, '%')) " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> searchCommentsByText(@Param("searchText") String searchText, Pageable pageable);

    /**
     * Find comments by user ID.
     * @param userId The user ID
     * @param pageable Pagination information
     * @return Page of user's comments
     */
    @Query("SELECT c FROM Comment c " +
           "WHERE c.user.id = :userId " +
           "AND c.isDeleted = false " +
           "ORDER BY c.createdAt DESC")
    Page<Comment> findCommentsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * Delete all comments for a content item (when content is deleted).
     * @param content The content whose comments to delete
     */
    void deleteByContent(Content content);

    /**
     * Delete all comments by a user (when user is deleted).
     * @param user The user whose comments to delete
     */
    void deleteByUser(User user);
}
