package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.request.CreateCommentRequest;
import com.reelsnack.reelsnack.dto.request.UpdateCommentRequest;
import com.reelsnack.reelsnack.dto.response.CommentResponse;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing comments.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/content")
@RequiredArgsConstructor
@Tag(name = "Comments", description = "Comment management operations")
public class CommentController {

    private final CommentService commentService;

    // ===== Comment CRUD Operations =====

    @PostMapping("/{contentId}/comments")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Create a comment", 
              description = "Create a new comment on content or reply to an existing comment")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "Comment created successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Content or parent comment not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<CommentResponse> createComment(
            @Parameter(description = "ID of the content to comment on", required = true, example = "123")
            @PathVariable @Positive Long contentId,
            
            @Parameter(description = "Comment creation request", required = true)
            @Valid @RequestBody CreateCommentRequest request,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        CommentResponse comment = commentService.createComment(contentId, request, userDetails.getUsername());
        return ResponseEntity.status(HttpStatus.CREATED).body(comment);
    }

    @PutMapping("/comments/{commentId}")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Update a comment", 
              description = "Update the text of an existing comment (only by the comment author)")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Comment updated successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Invalid request data or comment is deleted"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Not authorized to update this comment"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Comment not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<CommentResponse> updateComment(
            @Parameter(description = "ID of the comment to update", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @Parameter(description = "Comment update request", required = true)
            @Valid @RequestBody UpdateCommentRequest request,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        CommentResponse comment = commentService.updateComment(commentId, request, userDetails.getUsername());
        return ResponseEntity.ok(comment);
    }

    @DeleteMapping("/comments/{commentId}")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Delete a comment", 
              description = "Soft delete a comment (only by the comment author)")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "204", description = "Comment deleted successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Not authorized to delete this comment"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Comment not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Void> deleteComment(
            @Parameter(description = "ID of the comment to delete", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        commentService.deleteComment(commentId, userDetails.getUsername());
        return ResponseEntity.noContent().build();
    }

    // ===== Comment Query Operations =====

    @GetMapping("/{contentId}/comments")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get comments for content", 
              description = "Get paginated top-level comments for a specific content item")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved comments"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Content not found")
    })
    public ResponseEntity<Page<CommentResponse>> getCommentsForContent(
            @Parameter(description = "ID of the content to get comments for", required = true, example = "123")
            @PathVariable @Positive Long contentId,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort by field", example = "createdAt")
            @RequestParam(defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "Sort direction", example = "desc")
            @RequestParam(defaultValue = "desc") String sortDir,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        
        Page<CommentResponse> comments = commentService.getCommentsForContent(contentId, pageable, currentUsername);
        return ResponseEntity.ok(comments);
    }

    @GetMapping("/comments/{commentId}/replies")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get replies to a comment", 
              description = "Get paginated replies to a specific comment")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved replies"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Comment not found")
    })
    public ResponseEntity<Page<CommentResponse>> getRepliesForComment(
            @Parameter(description = "ID of the parent comment to get replies for", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size,
            
            @Parameter(description = "Sort by field", example = "createdAt")
            @RequestParam(defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "Sort direction", example = "asc")
            @RequestParam(defaultValue = "asc") String sortDir,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        
        Page<CommentResponse> replies = commentService.getRepliesForComment(commentId, pageable, currentUsername);
        return ResponseEntity.ok(replies);
    }

    @GetMapping("/comments/{commentId}")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get a specific comment", 
              description = "Get details of a specific comment by ID")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved comment"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Comment not found")
    })
    public ResponseEntity<CommentResponse> getCommentById(
            @Parameter(description = "ID of the comment to retrieve", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        CommentResponse comment = commentService.getCommentById(commentId, currentUsername);
        return ResponseEntity.ok(comment);
    }

    // ===== Comment Like Operations =====

    @PostMapping("/comments/{commentId}/like")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Like/Unlike a comment", 
              description = "Toggle like status on a comment")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Like status toggled successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Comment not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<CommentResponse> toggleCommentLike(
            @Parameter(description = "ID of the comment to like/unlike", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        CommentResponse comment = commentService.toggleCommentLike(commentId, userDetails.getUsername());
        return ResponseEntity.ok(comment);
    }
}
