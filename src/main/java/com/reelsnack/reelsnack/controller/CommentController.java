package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.request.CreateCommentRequest;
import com.reelsnack.reelsnack.dto.request.UpdateCommentRequest;
import com.reelsnack.reelsnack.dto.response.CommentResponse;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for managing comments on content.
 * Provides endpoints for creating, reading, updating, and deleting comments,
 * as well as comment interactions like liking.
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
@Tag(name = "Comments", description = "APIs for managing comments on content")
public class CommentController {

    private final CommentService commentService;

    // ===== Content Comments Endpoints =====

    @GetMapping("/content/{contentId}/comments")
    @PreAuthorize("permitAll()")
    @Operation(
        summary = "Get comments for content",
        description = "Retrieve paginated list of top-level comments for a specific content"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved comments"),
        @ApiResponse(responseCode = "404", description = "Content not found")
    })
    public ResponseEntity<Page<CommentResponse>> getCommentsForContent(
            @Parameter(description = "Content ID", required = true, example = "123")
            @PathVariable @Positive Long contentId,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort by field", example = "createdAt")
            @RequestParam(defaultValue = "createdAt") String sortBy,
            
            @Parameter(description = "Sort direction", example = "desc")
            @RequestParam(defaultValue = "desc") String sortDir,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        Page<CommentResponse> comments = commentService.getCommentsForContent(contentId, pageable, currentUsername);
        
        return ResponseEntity.ok(comments);
    }

    @PostMapping("/content/{contentId}/comments")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Create comment on content",
        description = "Create a new comment on a specific content"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Comment created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "404", description = "Content not found")
    })
    public ResponseEntity<CommentResponse> createComment(
            @Parameter(description = "Content ID", required = true, example = "123")
            @PathVariable @Positive Long contentId,
            
            @Parameter(description = "Comment creation request", required = true)
            @Valid @RequestBody CreateCommentRequest request,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        CommentResponse comment = commentService.createComment(contentId, request, userDetails.getUsername());
        return ResponseEntity.status(HttpStatus.CREATED).body(comment);
    }

    // ===== Comment Management Endpoints =====

    @GetMapping("/comments/{commentId}/replies")
    @PreAuthorize("permitAll()")
    @Operation(
        summary = "Get replies to a comment",
        description = "Retrieve paginated list of replies to a specific comment"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved replies"),
        @ApiResponse(responseCode = "404", description = "Comment not found")
    })
    public ResponseEntity<Page<CommentResponse>> getRepliesForComment(
            @Parameter(description = "Comment ID", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "createdAt"));
        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        
        Page<CommentResponse> replies = commentService.getRepliesForComment(commentId, pageable, currentUsername);
        return ResponseEntity.ok(replies);
    }

    @PutMapping("/comments/{commentId}")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Update comment",
        description = "Update an existing comment (only by the comment author)"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comment updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Not authorized to edit this comment"),
        @ApiResponse(responseCode = "404", description = "Comment not found")
    })
    public ResponseEntity<CommentResponse> updateComment(
            @Parameter(description = "Comment ID", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @Parameter(description = "Comment update request", required = true)
            @Valid @RequestBody UpdateCommentRequest request,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        CommentResponse updatedComment = commentService.updateComment(commentId, request, userDetails.getUsername());
        return ResponseEntity.ok(updatedComment);
    }

    @DeleteMapping("/comments/{commentId}")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Delete comment",
        description = "Delete a comment (only by the comment author or admin)"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Comment deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this comment"),
        @ApiResponse(responseCode = "404", description = "Comment not found")
    })
    public ResponseEntity<Void> deleteComment(
            @Parameter(description = "Comment ID", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        commentService.deleteComment(commentId, userDetails.getUsername());
        return ResponseEntity.noContent().build();
    }

    // ===== Comment Interaction Endpoints =====

    @PostMapping("/comments/{commentId}/like")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Like/Unlike comment",
        description = "Toggle like status on a comment"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comment like status toggled successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "404", description = "Comment not found")
    })
    public ResponseEntity<CommentResponse> toggleCommentLike(
            @Parameter(description = "Comment ID", required = true, example = "456")
            @PathVariable @Positive Long commentId,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        CommentResponse comment = commentService.toggleCommentLike(commentId, userDetails.getUsername());
        return ResponseEntity.ok(comment);
    }
}
