package com.reelsnack.reelsnack.controller;

import com.reelsnack.reelsnack.dto.response.FollowStatsResponse;
import com.reelsnack.reelsnack.dto.response.UserFollowResponse;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.service.UserFollowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing user follow relationships.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Tag(name = "User Follow", description = "User follow relationship management")
public class UserFollowController {

    private final UserFollowService userFollowService;

    // ===== Follow/Unfollow Operations =====

    @PostMapping("/{username}/follow")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Follow a user", 
              description = "Follow another user to see their content in your feed")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully followed user"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Cannot follow yourself or already following"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<UserFollowResponse> followUser(
            @Parameter(description = "Username of the user to follow", required = true, example = "chef_maria")
            @PathVariable String username,
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        UserFollowResponse response = userFollowService.followUser(userDetails.getUsername(), username);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{username}/follow")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Unfollow a user", 
              description = "Unfollow a user to stop seeing their content in your feed")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "204", description = "Successfully unfollowed user"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Not following this user"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Void> unfollowUser(
            @Parameter(description = "Username of the user to unfollow", required = true, example = "chef_maria")
            @PathVariable String username,
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        userFollowService.unfollowUser(userDetails.getUsername(), username);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{username}/toggle-follow")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Toggle follow status", 
              description = "Follow if not following, unfollow if following")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Follow status toggled successfully"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Cannot follow yourself"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<UserFollowResponse> toggleFollow(
            @Parameter(description = "Username of the user to toggle follow", required = true, example = "chef_maria")
            @PathVariable String username,
            @AuthenticationPrincipal UserDetailsImpl userDetails) {
        
        UserFollowResponse response = userFollowService.toggleFollow(userDetails.getUsername(), username);
        return ResponseEntity.ok(response);
    }

    // ===== Query Operations =====

    @GetMapping("/{username}/followers")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get user's followers", 
              description = "Get a paginated list of users who follow the specified user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved followers"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<Page<UserFollowResponse.UserSummaryResponse>> getFollowers(
            @Parameter(description = "Username of the user whose followers to get", required = true, example = "chef_maria")
            @PathVariable String username,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        
        Page<UserFollowResponse.UserSummaryResponse> followers = 
            userFollowService.getFollowers(username, pageable, currentUsername);
        
        return ResponseEntity.ok(followers);
    }

    @GetMapping("/{username}/following")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get users that a user is following", 
              description = "Get a paginated list of users that the specified user is following")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved following list"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<Page<UserFollowResponse.UserSummaryResponse>> getFollowing(
            @Parameter(description = "Username of the user whose following list to get", required = true, example = "chef_maria")
            @PathVariable String username,
            
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "20")
            @RequestParam(defaultValue = "20") int size,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        
        Page<UserFollowResponse.UserSummaryResponse> following = 
            userFollowService.getFollowing(username, pageable, currentUsername);
        
        return ResponseEntity.ok(following);
    }

    @GetMapping("/{username}/follow-stats")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Get user's follow statistics", 
              description = "Get follower count, following count, and relationship status with current user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved follow statistics"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "User not found")
    })
    public ResponseEntity<FollowStatsResponse> getFollowStats(
            @Parameter(description = "Username of the user whose stats to get", required = true, example = "chef_maria")
            @PathVariable String username,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        String currentUsername = userDetails != null ? userDetails.getUsername() : null;
        FollowStatsResponse stats = userFollowService.getFollowStats(username, currentUsername);
        
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/{followerUsername}/is-following/{followingUsername}")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Check if user A follows user B", 
              description = "Check if one user follows another user")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully checked follow status")
    })
    public ResponseEntity<Boolean> isFollowing(
            @Parameter(description = "Username of potential follower", required = true, example = "user1")
            @PathVariable String followerUsername,
            
            @Parameter(description = "Username of potential following", required = true, example = "user2")
            @PathVariable String followingUsername) {

        boolean isFollowing = userFollowService.isFollowing(followerUsername, followingUsername);
        return ResponseEntity.ok(isFollowing);
    }

    @GetMapping("/suggestions")
    @SecurityRequirement(name = "bearerAuth")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get suggested users to follow", 
              description = "Get personalized suggestions of users to follow based on your network")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieved suggestions"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Page<UserFollowResponse.UserSummaryResponse>> getSuggestedUsers(
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Number of items per page", example = "10")
            @RequestParam(defaultValue = "10") int size,
            
            @AuthenticationPrincipal UserDetailsImpl userDetails) {

        Pageable pageable = PageRequest.of(page, size);
        Page<UserFollowResponse.UserSummaryResponse> suggestions = 
            userFollowService.getSuggestedUsers(userDetails.getUsername(), pageable);
        
        return ResponseEntity.ok(suggestions);
    }
}
