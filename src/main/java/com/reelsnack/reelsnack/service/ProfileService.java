package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.ChangePasswordRequest;
import com.reelsnack.reelsnack.dto.request.UpdateProfileRequest;
import com.reelsnack.reelsnack.dto.response.ProfileResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ProfileException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.mapper.UserMapper;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserPreference;
import com.reelsnack.reelsnack.repository.UserPreferenceRepository;
import com.reelsnack.reelsnack.repository.UserRepository;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProfileService {
    private final UserRepository userRepository;
    private final UserPreferenceRepository userPreferenceRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserMapper userMapper;

    @Transactional(readOnly = true)
    public ProfileResponse getCurrentUserProfile(UserDetailsImpl currentUser) {
        // Find the user
        User user = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", currentUser.getId()));
        
        // Get or create default preferences
        UserPreference preferences = user.getUserPreference();
        if (preferences == null) {
            // Create default preferences if they don't exist
            preferences = user.createDefaultUserPreference();
            userRepository.save(user); // Save the user to persist the new preference
        }
                
        return userMapper.toProfileResponse(user, preferences);
    }

    @Transactional
    public ProfileResponse updateProfile(UserDetailsImpl currentUser, UpdateProfileRequest request) {
        // Find the user
        User user = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", currentUser.getId()));

        // Update user fields if they are provided in the request
        if (request.getFirstName() != null) user.setFirstName(request.getFirstName());
        if (request.getLastName() != null) user.setLastName(request.getLastName());
        if (request.getPhoneNumber() != null) user.setPhoneNumber(request.getPhoneNumber());
        if (request.getProfilePicture() != null) user.setProfilePicture(request.getProfilePicture());
        if (request.getBio() != null) user.setBio(request.getBio());

        try {
            // Save the user
            User updatedUser = userRepository.save(user);
            
            // Get or create user preferences
            UserPreference preferences = updatedUser.getUserPreference();
            if (preferences == null) {
                // Create default preferences if they don't exist
                preferences = updatedUser.createDefaultUserPreference();
                userRepository.save(updatedUser); // Save the user to persist the new preference
            }
                    
            return userMapper.toProfileResponse(updatedUser, preferences);
        } catch (Exception e) {
            log.error("Error updating profile for user: " + user.getUsername(), e);
            throw new ProfileException("Failed to update profile: " + e.getMessage());
        }
    }

    @Transactional
    public void changePassword(UserDetailsImpl currentUser, ChangePasswordRequest request) {
        if (!request.getNewPassword().equals(request.getConfirmNewPassword())) {
            throw new BadRequestException("New passwords do not match");
        }

        User user = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", currentUser.getId()));

        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new BadRequestException("Current password is incorrect");
        }

        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
    }


}
