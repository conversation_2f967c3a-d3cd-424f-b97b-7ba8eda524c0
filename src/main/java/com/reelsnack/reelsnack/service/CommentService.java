package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.CreateCommentRequest;
import com.reelsnack.reelsnack.dto.request.UpdateCommentRequest;
import com.reelsnack.reelsnack.dto.response.CommentResponse;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.exception.UnauthorizedException;
import com.reelsnack.reelsnack.model.Comment;
import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.repository.CommentRepository;
import com.reelsnack.reelsnack.repository.ContentRepository;
import com.reelsnack.reelsnack.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service class for managing comments on content.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class CommentService {

    private final CommentRepository commentRepository;
    private final ContentRepository contentRepository;
    private final UserRepository userRepository;

    // ===== Comment CRUD Operations =====

    /**
     * Create a new comment on content.
     * @param contentId The ID of the content to comment on
     * @param request The comment creation request
     * @param username The username of the commenter
     * @return The created comment response
     */
    @Transactional
    public CommentResponse createComment(Long contentId, CreateCommentRequest request, String username) {
        log.debug("Creating comment for content {} by user {}", contentId, username);

        // Find the content
        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        // Find the user
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        // Find parent comment if this is a reply
        Comment parentComment = null;
        if (request.getParentCommentId() != null) {
            parentComment = commentRepository.findByIdAndIsDeletedFalse(request.getParentCommentId())
                .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", request.getParentCommentId()));
        }

        // Create the comment
        Comment comment = Comment.builder()
            .content(content)
            .user(user)
            .parentComment(parentComment)
            .commentText(request.getCommentText())
            .isEdited(false)
            .isDeleted(false)
            .likeCount(0L)
            .replyCount(0L)
            .build();

        Comment savedComment = commentRepository.save(comment);
        log.info("Created comment {} for content {} by user {}", savedComment.getId(), contentId, username);

        return mapToCommentResponse(savedComment, username);
    }

    /**
     * Get comments for a specific content.
     * @param contentId The content ID
     * @param pageable Pagination information
     * @param currentUsername The current user's username (for permission checks)
     * @return Page of comments
     */
    public Page<CommentResponse> getCommentsForContent(Long contentId, Pageable pageable, String currentUsername) {
        log.debug("Getting comments for content {} with pagination {}", contentId, pageable);

        // Verify content exists
        if (!contentRepository.existsById(contentId)) {
            throw new ResourceNotFoundException("Content", "id", contentId);
        }

        Page<Comment> comments = commentRepository.findTopLevelCommentsWithUser(contentId, pageable);
        return comments.map(comment -> mapToCommentResponse(comment, currentUsername));
    }

    /**
     * Get replies for a specific comment.
     * @param commentId The parent comment ID
     * @param pageable Pagination information
     * @param currentUsername The current user's username
     * @return Page of reply comments
     */
    public Page<CommentResponse> getRepliesForComment(Long commentId, Pageable pageable, String currentUsername) {
        log.debug("Getting replies for comment {} with pagination {}", commentId, pageable);

        // Verify parent comment exists
        if (!commentRepository.existsById(commentId)) {
            throw new ResourceNotFoundException("Comment", "id", commentId);
        }

        Page<Comment> replies = commentRepository.findRepliesWithUser(commentId, pageable);
        return replies.map(comment -> mapToCommentResponse(comment, currentUsername));
    }

    /**
     * Update an existing comment.
     * @param commentId The comment ID to update
     * @param request The update request
     * @param username The username of the requester
     * @return The updated comment response
     */
    @Transactional
    public CommentResponse updateComment(Long commentId, UpdateCommentRequest request, String username) {
        log.debug("Updating comment {} by user {}", commentId, username);

        Comment comment = commentRepository.findByIdAndIsDeletedFalse(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        // Check if user owns the comment
        if (!comment.getUser().getUsername().equals(username)) {
            throw new UnauthorizedException("You can only edit your own comments");
        }

        // Update the comment
        comment.setCommentText(request.getCommentText());
        comment.markAsEdited();

        Comment updatedComment = commentRepository.save(comment);
        log.info("Updated comment {} by user {}", commentId, username);

        return mapToCommentResponse(updatedComment, username);
    }

    /**
     * Delete a comment (soft delete).
     * @param commentId The comment ID to delete
     * @param username The username of the requester
     */
    @Transactional
    public void deleteComment(Long commentId, String username) {
        log.debug("Deleting comment {} by user {}", commentId, username);

        Comment comment = commentRepository.findByIdAndIsDeletedFalse(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        // Check if user owns the comment or is admin
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        boolean canDelete = comment.getUser().getUsername().equals(username) || 
                           user.getRole().name().equals("ADMIN");

        if (!canDelete) {
            throw new UnauthorizedException("You can only delete your own comments");
        }

        // Soft delete the comment
        comment.softDelete();
        commentRepository.save(comment);

        log.info("Deleted comment {} by user {}", commentId, username);
    }

    // ===== Comment Interaction Operations =====

    /**
     * Like or unlike a comment.
     * @param commentId The comment ID to like/unlike
     * @param username The username of the user
     * @return The updated comment response
     */
    @Transactional
    public CommentResponse toggleCommentLike(Long commentId, String username) {
        log.debug("Toggling like for comment {} by user {}", commentId, username);

        Comment comment = commentRepository.findByIdAndIsDeletedFalse(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        boolean isLiked = comment.getLikedBy().contains(user);

        if (isLiked) {
            // Unlike the comment
            comment.getLikedBy().remove(user);
            comment.decrementLikeCount();
            log.debug("User {} unliked comment {}", username, commentId);
        } else {
            // Like the comment
            comment.getLikedBy().add(user);
            comment.incrementLikeCount();
            log.debug("User {} liked comment {}", username, commentId);
        }

        Comment updatedComment = commentRepository.save(comment);
        return mapToCommentResponse(updatedComment, username);
    }

    // ===== Helper Methods =====

    /**
     * Map Comment entity to CommentResponse DTO.
     * @param comment The comment entity
     * @param currentUsername The current user's username
     * @return The comment response DTO
     */
    private CommentResponse mapToCommentResponse(Comment comment, String currentUsername) {
        boolean isLikedByCurrentUser = currentUsername != null && 
            commentRepository.existsByIdAndLikedByUsername(comment.getId(), currentUsername);

        boolean canEdit = currentUsername != null && 
            comment.getUser().getUsername().equals(currentUsername);

        boolean canDelete = canEdit; // For now, only owner can delete

        return CommentResponse.builder()
            .id(comment.getId())
            .contentId(comment.getContentId())
            .parentCommentId(comment.getParentCommentId())
            .commentText(comment.getCommentText())
            .user(mapToUserSummary(comment.getUser()))
            .isEdited(comment.getIsEdited())
            .isDeleted(comment.getIsDeleted())
            .likeCount(comment.getLikeCount())
            .replyCount(comment.getReplyCount())
            .isLikedByCurrentUser(isLikedByCurrentUser)
            .canEdit(canEdit)
            .canDelete(canDelete)
            .createdAt(comment.getCreatedAt())
            .updatedAt(comment.getUpdatedAt())
            .build();
    }

    /**
     * Map User entity to UserSummaryResponse DTO.
     * @param user The user entity
     * @return The user summary response DTO
     */
    private CommentResponse.UserSummaryResponse mapToUserSummary(User user) {
        return CommentResponse.UserSummaryResponse.builder()
            .id(user.getId())
            .username(user.getUsername())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .profilePictureUrl(user.getProfilePicture())
            .bio(user.getBio())
            .isVerified(false) // TODO: Add isVerified field to User entity
            .role(user.getRole() != null ? user.getRole().name() : "USER")
            .build();
    }
}
