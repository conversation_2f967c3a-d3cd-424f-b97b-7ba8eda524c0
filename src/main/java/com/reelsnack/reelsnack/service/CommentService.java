package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.CreateCommentRequest;
import com.reelsnack.reelsnack.dto.request.UpdateCommentRequest;
import com.reelsnack.reelsnack.dto.response.CommentResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.exception.UnauthorizedException;
import com.reelsnack.reelsnack.model.Comment;
import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.repository.CommentRepository;
import com.reelsnack.reelsnack.repository.ContentRepository;
import com.reelsnack.reelsnack.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class for managing comments.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class CommentService {

    private final CommentRepository commentRepository;
    private final ContentRepository contentRepository;
    private final UserRepository userRepository;

    // ===== Comment CRUD Operations =====

    /**
     * Create a new comment on content.
     * @param contentId ID of the content to comment on
     * @param request Comment creation request
     * @param username Username of the commenter
     * @return Created comment response
     */
    @Transactional
    public CommentResponse createComment(Long contentId, CreateCommentRequest request, String username) {
        log.debug("Creating comment for content {} by user {}", contentId, username);

        // Find content
        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        // Find user
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        // Find parent comment if specified
        Comment parentComment = null;
        if (request.getParentCommentId() != null) {
            parentComment = commentRepository.findById(request.getParentCommentId())
                .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", request.getParentCommentId()));
            
            // Ensure parent comment belongs to the same content
            if (!parentComment.getContent().getId().equals(contentId)) {
                throw new BadRequestException("Parent comment does not belong to the specified content");
            }
        }

        // Create comment
        Comment comment = Comment.builder()
            .content(content)
            .user(user)
            .parentComment(parentComment)
            .commentText(request.getCommentText())
            .build();

        Comment savedComment = commentRepository.save(comment);
        log.info("Comment {} created successfully for content {} by user {}", 
                savedComment.getId(), contentId, username);

        return mapToCommentResponse(savedComment, username);
    }

    /**
     * Update an existing comment.
     * @param commentId ID of the comment to update
     * @param request Comment update request
     * @param username Username of the user updating the comment
     * @return Updated comment response
     */
    @Transactional
    public CommentResponse updateComment(Long commentId, UpdateCommentRequest request, String username) {
        log.debug("Updating comment {} by user {}", commentId, username);

        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        // Check if user owns the comment
        if (!comment.getUser().getUsername().equals(username)) {
            throw new UnauthorizedException("You can only update your own comments");
        }

        // Check if comment is deleted
        if (comment.getIsDeleted()) {
            throw new BadRequestException("Cannot update a deleted comment");
        }

        // Update comment
        comment.setCommentText(request.getCommentText());
        comment.markAsEdited();

        Comment savedComment = commentRepository.save(comment);
        log.info("Comment {} updated successfully by user {}", commentId, username);

        return mapToCommentResponse(savedComment, username);
    }

    /**
     * Delete a comment (soft delete).
     * @param commentId ID of the comment to delete
     * @param username Username of the user deleting the comment
     */
    @Transactional
    public void deleteComment(Long commentId, String username) {
        log.debug("Deleting comment {} by user {}", commentId, username);

        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        // Check if user owns the comment
        if (!comment.getUser().getUsername().equals(username)) {
            throw new UnauthorizedException("You can only delete your own comments");
        }

        // Soft delete
        comment.softDelete();
        commentRepository.save(comment);
        
        log.info("Comment {} deleted successfully by user {}", commentId, username);
    }

    // ===== Comment Query Operations =====

    /**
     * Get comments for a content item.
     * @param contentId ID of the content
     * @param pageable Pagination information
     * @param currentUsername Current user's username for like status
     * @return Page of comments
     */
    public Page<CommentResponse> getCommentsForContent(Long contentId, Pageable pageable, String currentUsername) {
        log.debug("Getting comments for content {}", contentId);

        Content content = contentRepository.findById(contentId)
            .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));

        Page<Comment> comments = commentRepository.findTopLevelCommentsByContentId(contentId, pageable);
        return comments.map(comment -> mapToCommentResponse(comment, currentUsername));
    }

    /**
     * Get replies to a comment.
     * @param commentId ID of the parent comment
     * @param pageable Pagination information
     * @param currentUsername Current user's username for like status
     * @return Page of reply comments
     */
    public Page<CommentResponse> getRepliesForComment(Long commentId, Pageable pageable, String currentUsername) {
        log.debug("Getting replies for comment {}", commentId);

        Comment parentComment = commentRepository.findById(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        Page<Comment> replies = commentRepository.findRepliesByParentCommentId(commentId, pageable);
        return replies.map(comment -> mapToCommentResponse(comment, currentUsername));
    }

    /**
     * Get a specific comment by ID.
     * @param commentId ID of the comment
     * @param currentUsername Current user's username for like status
     * @return Comment response
     */
    public CommentResponse getCommentById(Long commentId, String currentUsername) {
        log.debug("Getting comment {}", commentId);

        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        return mapToCommentResponse(comment, currentUsername);
    }

    // ===== Comment Like Operations =====

    /**
     * Toggle like status on a comment.
     * @param commentId ID of the comment to like/unlike
     * @param username Username of the user
     * @return Updated comment response
     */
    @Transactional
    public CommentResponse toggleCommentLike(Long commentId, String username) {
        log.debug("Toggling like for comment {} by user {}", commentId, username);

        Comment comment = commentRepository.findById(commentId)
            .orElseThrow(() -> new ResourceNotFoundException("Comment", "id", commentId));

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));

        boolean isLiked = comment.getLikedBy().contains(user);

        if (isLiked) {
            comment.getLikedBy().remove(user);
            comment.decrementLikeCount();
            log.debug("User {} unliked comment {}", username, commentId);
        } else {
            comment.getLikedBy().add(user);
            comment.incrementLikeCount();
            log.debug("User {} liked comment {}", username, commentId);
        }

        Comment savedComment = commentRepository.save(comment);
        return mapToCommentResponse(savedComment, username);
    }

    // ===== Helper Methods =====

    /**
     * Map Comment entity to CommentResponse DTO.
     */
    private CommentResponse mapToCommentResponse(Comment comment, String currentUsername) {
        boolean isLikedByCurrentUser = currentUsername != null && 
            commentRepository.existsByIdAndLikedByUsername(comment.getId(), currentUsername);

        return CommentResponse.builder()
            .id(comment.getId())
            .contentId(comment.getContentId())
            .parentCommentId(comment.getParentCommentId())
            .commentText(comment.getCommentText())
            .isEdited(comment.getIsEdited())
            .isDeleted(comment.getIsDeleted())
            .likeCount(comment.getLikeCount())
            .replyCount(commentRepository.countByParentCommentAndIsDeletedFalse(comment))
            .isLikedByCurrentUser(isLikedByCurrentUser)
            .user(mapToCommentUserResponse(comment.getUser()))
            .createdAt(comment.getCreatedAt())
            .updatedAt(comment.getUpdatedAt())
            .build();
    }

    /**
     * Map User entity to CommentUserResponse DTO.
     */
    private CommentResponse.CommentUserResponse mapToCommentUserResponse(User user) {
        return CommentResponse.CommentUserResponse.builder()
            .id(user.getId())
            .username(user.getUsername())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .profilePictureUrl(user.getProfilePicture())
            .isVerified(user.isVerified())
            .role(user.getRole() != null ? user.getRole().name() : "USER")
            .build();
    }
}
