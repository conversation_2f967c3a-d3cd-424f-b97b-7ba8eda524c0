package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.SignupRequest;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.Role;

import java.util.List;

public interface UserService {
    /**
     * Register a new user with the provided signup request
     * 
     * @param signupRequest The signup request containing user details
     * @return The created user
     */
    User registerUser(SignupRequest signupRequest);
    
    /**
     * Check if a username is already taken
     * 
     * @param username The username to check
     * @return true if the username is already taken, false otherwise
     */
    boolean existsByUsername(String username);
    
    /**
     * Check if an email is already in use
     * 
     * @param email The email to check
     * @return true if the email is already in use, false otherwise
     */
    boolean existsByEmail(String email);
    
    /**
     * Find a user by username
     * 
     * @param username The username to search for
     * @return The user if found, or null
     */
    User findByUsername(String username);
    
    /**
     * Find a user by ID
     * 
     * @param id The user ID
     * @return The user if found, or null
     */
    User findById(Long id);
    
    /**
     * Save a user
     * 
     * @param user The user to save
     * @return The saved user
     */
    User save(User user);
    
    /**
     * Get all users
     * 
     * @return List of all users
     */
    List<User> findAll();
    
    /**
     * Delete a user by ID
     * 
     * @param id The ID of the user to delete
     */
    void deleteById(Long id);
    
    /**
     * Verify a user's email
     * 
     * @param user The user whose email to verify
     */
    void verifyUserEmail(User user);
    
    /**
     * Resend verification email
     * 
     * @param email The email address to resend the verification to
     */
    void resendVerificationEmail(String email);
}
