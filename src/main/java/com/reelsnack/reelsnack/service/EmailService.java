package com.reelsnack.reelsnack.service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.HashMap;
import java.util.Map;

@Service
public class EmailService {

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    
    @Value("${app.email.sender}")
    private String fromEmail;
    
    @Value("${app.frontend.url}")
    private String frontendUrl;

    public EmailService(JavaMailSender mailSender, TemplateEngine templateEngine) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
    }

    @Async
    public void sendVerificationEmail(String to, String name, String token) {
        String subject = "Verify your email address";
        String verificationUrl = frontendUrl + "/verify-email?token=" + token;
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", name);
        variables.put("verificationUrl", verificationUrl);
        
        String content = buildEmailContent("email/verification-email", variables);
        sendEmail(to, subject, content, true);
    }
    
    @Async
    public void sendPasswordResetEmail(String to, String name, String token) {
        String subject = "Reset your password";
        String resetUrl = frontendUrl + "/reset-password?token=" + token;
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("name", name);
        variables.put("resetUrl", resetUrl);
        
        String content = buildEmailContent("email/password-reset-email", variables);
        sendEmail(to, subject, content, true);
    }
    
    private String buildEmailContent(String templateName, Map<String, Object> variables) {
        Context context = new Context();
        context.setVariables(variables);
        return templateEngine.process(templateName, context);
    }
    
    private void sendEmail(String to, String subject, String content, boolean isHtml) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, "utf-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, isHtml);
            
            mailSender.send(mimeMessage);
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }
}
