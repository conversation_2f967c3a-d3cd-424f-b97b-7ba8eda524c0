package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.exception.TokenValidationException;
import com.reelsnack.reelsnack.security.*;
import com.reelsnack.reelsnack.dto.request.LoginRequest;
import com.reelsnack.reelsnack.dto.request.SignupRequest;
import com.reelsnack.reelsnack.dto.response.JwtResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.mapper.UserMapper;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserPreference;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * Service class for handling authentication and user registration.
 */
@Service
public class AuthService {
    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final UserDetailsServiceImpl userDetailsService;
    private final UserMapper userMapper;
    private final TokenService jwtTokenService;
    private final EmailService emailService;

    @Autowired
    public AuthService(AuthenticationManager authenticationManager,
                       UserRepository userRepository,
                       PasswordEncoder passwordEncoder,
                       JwtUtils jwtUtils,
                       UserDetailsServiceImpl userDetailsService,
                       UserMapper userMapper,
                       TokenService jwtTokenService,
                       EmailService emailService) {
        this.authenticationManager = authenticationManager;
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.jwtUtils = jwtUtils;
        this.userDetailsService = userDetailsService;
        this.userMapper = userMapper;
        this.jwtTokenService = jwtTokenService;
        this.emailService = emailService;
    }

    @Transactional
    public TokenPair authenticateUser(LoginRequest loginRequest) {
        // Authenticate user
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginRequest.getUsernameOrEmail().toLowerCase(),
                loginRequest.getPassword()
            )
        );

        // Generate token pair
        return jwtTokenService.generateTokenPair(authentication);
    }

    /**
     * Register a new user.
     *
     * @param signUpRequest the signup request
     */
    @Transactional
    public void registerUser(SignupRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            throw new BadRequestException("Error: Username is already taken!");
        }
        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            throw new BadRequestException("Error: Email is already in use!");
        }
        
        User user = userMapper.toEntity(signUpRequest);
        user.setPassword(passwordEncoder.encode(signUpRequest.getPassword()));
        user.setEnabled(false);
        
        if (user.getRole() == null) {
            user.setRole(Role.ROLE_USER);
        }
        
        if (signUpRequest.getRoles() != null && !signUpRequest.getRoles().isEmpty()) {
            signUpRequest.getRoles().forEach(role -> {
                if ("admin".equalsIgnoreCase(role)) {
                    user.setRole(Role.ROLE_ADMIN);
                } else if ("restaurant".equalsIgnoreCase(role)) {
                    user.setRole(Role.ROLE_RESTAURANT);
                } else if ("driver".equalsIgnoreCase(role)) {
                    user.setRole(Role.ROLE_DRIVER);
                }
            });
        }
        
        // Create and set default user preferences
        user.createDefaultUserPreference();
        
        // Save the user (cascade will save the user preference)
        User savedUser;
        try {
            savedUser = userRepository.save(user);
            System.out.println("User registered successfully with ID: {}"+ savedUser.getId());
        } catch (Exception e) {
            //System.out.println("Error saving user during registration: {}" + e.getMessage(), e);
            throw new RuntimeException("Failed to register user: " + e.getMessage(), e);
        }
        
        // Generate verification token and send email
        String token = jwtTokenService.generateAndSaveVerificationToken(savedUser);
        emailService.sendVerificationEmail(
            savedUser.getEmail(),
            savedUser.getUsername(),
            token
        );
    }
    
    /**
     * Verify a user's email.
     *
     * @param token the verification token
     */
    @Transactional
    public void verifyEmail(String token) {
        try {
            User user = jwtTokenService.validateVerificationToken(token);
            
            if (user.isEnabled()) {
                throw new BadRequestException("Email already verified");
            }
            
            user.setEnabled(true);
            userRepository.save(user);
            jwtTokenService.deleteVerificationToken(token);
        } catch (TokenValidationException e) {
            throw new BadRequestException("Invalid or expired verification token");
        }
    }
    
    /**
     * Refresh a token.
     *
     * @param refreshToken the refresh token
     * @return the new token pair
     */
    @Transactional
    public TokenPair refreshToken(String refreshToken) {
        return jwtTokenService.refreshToken(refreshToken);
    }
    
    /**
     * Logout a user.
     *
     * @param username the username
     */
    public void logout(String username) {
        jwtTokenService.revokeToken(username);
    }
}
