package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.exception.TokenValidationException;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.repository.UserRepository;
import com.reelsnack.reelsnack.security.TokenService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class PasswordServiceImpl implements PasswordService {

    private final UserRepository userRepository;
    private final TokenService jwtTokenService;
    private final EmailService emailService;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void requestPasswordReset(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));

        // Generate and save password reset token
        String token = jwtTokenService.generateAndSavePasswordResetToken(user);
        
        // Send password reset email
        emailService.sendPasswordResetEmail(user.getEmail(), user.getUsername(), token);
    }

    @Override
    @Transactional
    public void resetPassword(String token, String newPassword) {
        try {
            // Validate the token and get the user
            User user = jwtTokenService.validatePasswordResetToken(token);
            
            // Update the password
            user.setPassword(passwordEncoder.encode(newPassword));
            userRepository.save(user);
            
            // Delete the used token
            jwtTokenService.deletePasswordResetToken(user);
        } catch (TokenValidationException e) {
            throw new BadRequestException("Invalid or expired password reset token");
        }
    }
}
