package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.SignupRequest;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.mapper.UserMapper;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserPreference;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.UserRepository;
import com.reelsnack.reelsnack.security.TokenService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final TokenService jwtTokenService;
    private final EmailService emailService;

    @Override
    @Transactional
    public User registerUser(SignupRequest signupRequest) {
        // Map the signup request to a user entity
        User user = userMapper.toEntity(signupRequest);
        
        // Encode the password
        user.setPassword(passwordEncoder.encode(signupRequest.getPassword()));
        
        // Set default role if not provided
        if (user.getRole() == null) {
            user.setRole(Role.ROLE_USER);
        }
        
        // Create default user preferences
        UserPreference preferences = new UserPreference();
        preferences.setEmailNotifications(true);
        preferences.setPushNotifications(true);
        preferences.setSmsNotifications(false);
        preferences.setTheme("light");
        preferences.setLanguage("en");
        preferences.setUser(user);
        
        user.setUserPreference(preferences);
        
        // Save the user
        User savedUser = userRepository.save(user);
        
        // Generate and send verification email
        String token = jwtTokenService.generateAndSaveVerificationToken(savedUser);
        emailService.sendVerificationEmail(
            savedUser.getEmail(),
            savedUser.getUsername(),
            token
        );
        
        return savedUser;
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));
    }

    @Override
    public User findById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
    }

    @Override
    public User save(User user) {
        return userRepository.save(user);
    }

    @Override
    public List<User> findAll() {
        return userRepository.findAll();
    }

    @Override
    public void deleteById(Long id) {
        userRepository.deleteById(id);
    }
    
    @Override
    @Transactional
    public void verifyUserEmail(User user) {
        user.verifyEmail();
        userRepository.save(user);
    }
    
    @Override
    @Transactional
    public void resendVerificationEmail(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
                
        if (user.isEmailVerified()) {
            throw new IllegalStateException("Email is already verified");
        }
        
        // Generate a new verification token
        String token = jwtTokenService.generateAndSaveVerificationToken(user);
        
        // Send the verification email
        emailService.sendVerificationEmail(user.getEmail(), user.getUsername(), token);
    }
}
