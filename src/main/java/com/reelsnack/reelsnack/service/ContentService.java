package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.CreateContentRequest;
import com.reelsnack.reelsnack.dto.request.UpdateContentRequest;
import com.reelsnack.reelsnack.dto.response.ContentResponse;
import com.reelsnack.reelsnack.dto.response.UserProfileResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.exception.UnauthorizedException;
import com.reelsnack.reelsnack.exception.UnsupportedMediaTypeException;
import com.reelsnack.reelsnack.mapper.ContentMapper;
import com.reelsnack.reelsnack.mapper.UserMapper;
import com.reelsnack.reelsnack.model.Content;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.ContentType;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.model.enums.ServiceCategory;
import com.reelsnack.reelsnack.repository.ContentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.security.core.userdetails.UserDetails;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Set;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Service for managing content operations including creation, retrieval, updating, and deletion.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ContentService {
    private final ContentRepository contentRepository;
    private final UserService userService;
    private final FileStorageService fileStorageService;
    private final VideoProcessingService videoProcessingService;
    private final ContentMapper contentMapper;
    private final UserMapper userMapper;

    private static final Set<String> ALLOWED_CONTENT_TYPES = Set.of(
            "image/jpeg", "image/png", "image/gif",
            "video/mp4", "video/quicktime", "video/x-msvideo",
            "video/x-ms-wmv", "video/x-flv", "video/webm"
    );
    
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    
    @Value("${app.media.video.processing-timeout:300}")
    private int videoProcessingTimeout; // 5 minutes in seconds
    
    @Value("${app.media.video.max-resolution:3840x2160}")
    private String maxVideoResolution; // 4K UHD
    
    private int maxVideoWidth = 3840;
    private int maxVideoHeight = 2160;
    
    @PostConstruct
    public void init() {
        // Parse max video resolution on startup
        if (maxVideoResolution != null && maxVideoResolution.contains("x")) {
            try {
                String[] dimensions = maxVideoResolution.split("x");
                this.maxVideoWidth = Integer.parseInt(dimensions[0]);
                this.maxVideoHeight = Integer.parseInt(dimensions[1]);
            } catch (Exception e) {
                log.warn("Invalid max video resolution format: {}", maxVideoResolution, e);
            }
        }
    }

    /**
     * Creates new content with the provided media file and metadata
     * 
     * @param request The content creation request
     * @param mediaFile The media file to upload
     * @param userDetails The authenticated user
     * @return The created content response
     * @throws IOException If there's an error processing the file
     * @throws IllegalArgumentException If the request is invalid
     * @throws UnsupportedMediaTypeException If the media type is not supported
     */
    @Transactional(timeout = 300) // 5 minute timeout
    public ContentResponse createContent(CreateContentRequest request, MultipartFile mediaFile, UserDetails userDetails) 
            throws IOException, IllegalArgumentException, UnsupportedMediaTypeException {
        
        // Log the request for debugging
        if (log.isDebugEnabled()) {
            log.debug("Creating content with request: {}", request);
            log.debug("Media file: {} ({} bytes, {})", 
                mediaFile.getOriginalFilename(),
                mediaFile.getSize(),
                mediaFile.getContentType());
        }
        
        // Validate request parameters
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
        
        // Start timing the operation
        long startTime = System.currentTimeMillis();
        String username = userDetails.getUsername();
        User creator = userService.findByUsername(username);
        if (creator == null) {
            throw new ResourceNotFoundException("User", "username", username);
        }
        
        // Validate file
        validateMediaFile(mediaFile);

        // Upload media file
        String mediaUrl = fileStorageService.storeFile(mediaFile, "content/media");

        // Process media file based on type
        String thumbnailUrl = mediaUrl; // Default to media URL for non-video files
        Integer duration = null;
        Integer width = null;
        Integer height = null;

        // Process video files
        if (mediaFile.getContentType() != null && mediaFile.getContentType().startsWith("video/")) {
            try {
                VideoProcessingService.VideoProcessingResult result = videoProcessingService.processVideo(mediaFile);
                thumbnailUrl = result.getThumbnailUrl();
                duration = (int) result.getDuration();
                width = result.getWidth();
                height = result.getHeight();
                
                log.info("Processed video: duration={}s, dimensions={}x{}, thumbnail={}", 
                        duration, width, height, thumbnailUrl);
                        
            } catch (Exception e) {
                log.error("Error processing video: {}", e.getMessage(), e);
                throw new RuntimeException("Error processing video: " + e.getMessage(), e);
            }
        }

        // Create content with metadata
        Content content = Content.builder()
                .title(request.getTitle())
                .description(request.getDescription())
                .contentType(request.getContentType())
                .categories(request.getCategories())
                .isPublic(request.getIsPublic())
                .isPromoted(request.getIsPromoted())
                .mediaUrl(mediaUrl)
                .thumbnailUrl(thumbnailUrl)
                .creator(creator)
                .duration(duration)
                .width(width)
                .height(height)
                .build();

        Content savedContent = contentRepository.save(content);
        log.info("Created content with id: {}", savedContent.getId());
        
        return toContentResponse(savedContent);
    }

    /**
     * Validates the media file with comprehensive checks
     * @param file The file to validate
     * @throws IllegalArgumentException if the file is invalid
     * @throws UnsupportedMediaTypeException if the file type is not supported
     */
    private void validateMediaFile(MultipartFile file) {
        // Basic validation
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File is required");
        }
        
        // Content type validation
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType)) {
            throw UnsupportedMediaTypeException.of(contentType, ALLOWED_CONTENT_TYPES);
        }
        
        // File size validation
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException(String.format(
                "File size (%.2fMB) exceeds maximum allowed size of %dMB", 
                file.getSize() / (1024.0 * 1024.0), 
                MAX_FILE_SIZE / (1024 * 1024)
            ));
        }
        
        // Video-specific validations
        if (contentType.startsWith("video/")) {
            // Log warning for large video files
            if (file.getSize() > (50 * 1024 * 1024)) { // 50MB
                log.warn("Large video file detected: {}MB", file.getSize() / (1024 * 1024));
            }
            
            // Check video duration (if already known from metadata)
            // This would be more comprehensive with actual video processing
            
            // For better UX, we could also check the file extension matches the content type
            String filename = file.getOriginalFilename();
            if (filename != null) {
                String ext = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
                if (contentType.equals("video/mp4") && !ext.equals("mp4") ||
                    contentType.equals("video/quicktime") && !ext.equals("mov")) {
                    log.warn("File extension '{}' doesn't match content type '{}'", ext, contentType);
                }
            }
        }
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentFeed(Pageable pageable) {
        return contentRepository.findByIsPublicTrue(pageable)
                .map(this::toContentResponse);
    }

    @Transactional(readOnly = true)
    public ContentResponse getContentById(Long id) {
        Content content = contentRepository.findById(id)
                .filter(c -> c.getIsPublic() != null && c.getIsPublic())
                .orElseThrow(() -> new ResourceNotFoundException("Content", "id", id));
        
        // Increment view count
        content.incrementViewCount();
        contentRepository.save(content);
        
        return toContentResponse(content);
    }

    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByUser(String username, Pageable pageable) {
        User user = userService.findByUsername(username);
        if (user == null) {
            throw new ResourceNotFoundException("User", "username", username);
        }
        
        return contentRepository.findByCreatorAndIsPublic(user, true, pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByTypeAndUser(ContentType contentType, String username, Pageable pageable) {
        return contentRepository.findByUsernameAndContentTypeAndIsPublic(username, contentType, pageable)
                .map(this::toContentResponse);
    }

    @Transactional(readOnly = true)
    public Page<ContentResponse> searchContent(String query, Pageable pageable) {
        return contentRepository.findByTitleContainingIgnoreCaseAndIsPublic(query, true, pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> searchContentByType(String query, ContentType contentType, Pageable pageable) {
        return contentRepository.searchByTitleAndTypeAndIsPublic(query, contentType, true, pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getTrendingContent(Pageable pageable) {
        // Get content sorted by a combination of views, likes, and recency
        return contentRepository.findTrendingContent(pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional
    public ContentResponse updateContent(Long id, UpdateContentRequest request, MultipartFile mediaFile, UserDetails userDetails) throws IOException {
        Content content = contentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Content", "id", id));
        
        // Verify ownership
        if (!content.getCreator().getUsername().equals(userDetails.getUsername()) && 
            !userDetails.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"))) {
            throw new UnauthorizedException("Not authorized to update this content");
        }
        
        // Update fields if provided
        if (request.getTitle() != null) {
            content.setTitle(request.getTitle());
        }
        if (request.getDescription() != null) {
            content.setDescription(request.getDescription());
        }
        if (request.getContentType() != null) {
            content.setContentType(request.getContentType());
        }
        if (request.getCategories() != null) {
            content.setCategories(request.getCategories());
        }
        if (request.getTags() != null) {
            content.setTags(request.getTags());
        }
        if (request.getIsPublic() != null) {
            content.setIsPublic(request.getIsPublic());
        }
        if (request.getIsPromoted() != null) {
            content.setIsPromoted(request.getIsPromoted());
        }
        
        // Handle media file update if provided
        if (mediaFile != null && !mediaFile.isEmpty()) {
            // Delete old media file
            try {
                fileStorageService.deleteFile(content.getMediaUrl());
                if (content.getThumbnailUrl() != null && !content.getThumbnailUrl().equals(content.getMediaUrl())) {
                    fileStorageService.deleteFile(content.getThumbnailUrl());
                }
            } catch (Exception e) {
                log.error("Error deleting old media files for content {}: {}", id, e.getMessage());
            }
            
            // Upload new media file and generate thumbnail
            String mediaUrl = fileStorageService.storeFile(mediaFile, "content/media");
            String thumbnailUrl = generateThumbnail(mediaFile, mediaUrl);
            
            content.setMediaUrl(mediaUrl);
            content.setThumbnailUrl(thumbnailUrl);
        } else if (request.getThumbnailUrl() != null) {
            // If only thumbnail is being updated
            content.setThumbnailUrl(request.getThumbnailUrl());
        }
        
        content.setUpdatedAt(LocalDateTime.now());
        Content updatedContent = contentRepository.save(content);
        log.info("Updated content with id: {}", id);
        
        return toContentResponse(updatedContent);
    }
    
    @Transactional
    public boolean toggleLike(Long contentId, UserDetails userDetails) {
        Content content = contentRepository.findById(contentId)
                .orElseThrow(() -> new ResourceNotFoundException("Content", "id", contentId));
        
        User user = userService.findByUsername(userDetails.getUsername());
        if (user == null) {
            throw new ResourceNotFoundException("User", "username", userDetails.getUsername());
        }
        
        boolean isLiked = content.getLikedBy().contains(user);
        
        if (isLiked) {
            content.getLikedBy().remove(user);
            content.decrementLikeCount();
        } else {
            content.getLikedBy().add(user);
            content.incrementLikeCount();
        }
        
        contentRepository.save(content);
        log.info("User {} {} content with id: {}", 
                user.getUsername(), isLiked ? "unliked" : "liked", contentId);
        
        return !isLiked; // Return the new like status (true if now liked, false if unliked)
    }
    
    @Transactional(readOnly = true)
    public long getLikeCount(Long contentId) {
        return contentRepository.countLikesById(contentId);
    }
    
    @Transactional(readOnly = true)
    public boolean hasUserLikedContent(Long contentId, String username) {
        return contentRepository.existsByIdAndLikedByUsername(contentId, username);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByCategory(String categoryName, Pageable pageable) {
        try {
            ServiceCategory category = ServiceCategory.valueOf(categoryName.toUpperCase());
            return getContentByCategory(category, pageable);
        } catch (IllegalArgumentException e) {
            throw new BadRequestException("Invalid category: " + categoryName + ". Valid categories are: " +
                Arrays.stream(ServiceCategory.values()).map(Enum::name).collect(Collectors.joining(", ")));
        }
    }

    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByCategory(ServiceCategory category, Pageable pageable) {
        return contentRepository.findByCategoriesContainingAndIsPublicTrue(category, pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByType(ContentType contentType, Pageable pageable) {
        return contentRepository.findByContentTypeAndIsPublicTrue(contentType, pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByTypeAndCategory(ContentType contentType, String category, Pageable pageable) {
        return contentRepository.findByContentTypeAndCategoriesContainingAndIsPublicTrue(contentType, category, pageable)
                .map(this::toContentResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<ContentResponse> getContentByTags(List<String> tags, Pageable pageable) {
        return contentRepository.findByTagsInAndIsPublicTrue(tags, pageable)
                .map(this::toContentResponse);
    }

    @Transactional
    public void deleteContent(Long id, UserDetails userDetails) {
        // Get the username from UserDetails
        String username = userDetails.getUsername();
        
        // Find the user by username
        User user = userService.findByUsername(username);
        if (user == null) {
            throw new ResourceNotFoundException("User", "username", username);
        }
                
        // Find the content by ID
        Content content = contentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Content", "id", id));
                
        // Check if user is the owner or an admin
        if (!content.getCreator().equals(user) && (user.getRole() == null || !user.getRole().equals(Role.ROLE_ADMIN))) {
            throw new UnauthorizedException("You are not authorized to delete this content");
        }
        
        // Soft delete
        content.setIsDeleted(true);
        contentRepository.save(content);
        
        // Delete associated media files
        if (content.getMediaUrl() != null) {
            fileStorageService.deleteFile(content.getMediaUrl());
        }
        if (content.getThumbnailUrl() != null) {
            fileStorageService.deleteFile(content.getThumbnailUrl());
        }
        log.info("Deleted content with id: {}", id);
    }

    private String generateThumbnail(MultipartFile mediaFile, String mediaUrl) throws IOException {
        try {
            // Try to generate thumbnail from video
            if (mediaFile.getContentType() != null && mediaFile.getContentType().startsWith("video/")) {
                return videoProcessingService.generateThumbnail(mediaFile);
            }
            // For images, use the image itself as thumbnail
            return mediaUrl;
        } catch (Exception e) {
            log.warn("Failed to generate thumbnail: {}", e.getMessage());
            return mediaUrl; // Fallback to media URL if thumbnail generation fails
        }
    }
    
    private ContentResponse toContentResponse(Content content) {
        UserProfileResponse creatorProfile = userMapper.toUserProfileResponse(content.getCreator());
        long likeCount = content.getLikedBy() != null ? content.getLikedBy().size() : 0;
        
        return ContentResponse.builder()
                .id(content.getId())
                .title(content.getTitle())
                .description(content.getDescription())
                .mediaUrl(content.getMediaUrl())
                .thumbnailUrl(content.getThumbnailUrl())
                .contentType(content.getContentType())
                .creator(creatorProfile)
                .categories(content.getCategories())
                .tags(content.getTags())
                .viewCount(content.getViewCount())
                .likeCount(likeCount)
                .commentCount(content.getCommentCount())
                .shareCount(content.getShareCount())
                .isPublic(content.getIsPublic())
                .isPromoted(content.getIsPromoted())
                .createdAt(content.getCreatedAt())
                .updatedAt(content.getUpdatedAt())
                .build();
    }



    /**
     * Get all available content categories.
     * @return List of category names
     */
    public List<String> getAllCategories() {
        return Arrays.stream(ServiceCategory.values())
            .map(Enum::name)
            .collect(Collectors.toList());
    }
}
