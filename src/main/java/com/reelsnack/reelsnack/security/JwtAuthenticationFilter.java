package com.reelsnack.reelsnack.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Filter to validate JWT tokens in the request header and set the authentication in the security context.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    private static final String AUTH_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";
    
    private final TokenService tokenService;
    private final JwtUtils jwtUtils;
    private final UserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        // Skip authentication for public endpoints
        if (isPublicEndpoint(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String jwt = parseJwt(request);
            if (jwt == null) {
                log.warn("No JWT token found in request");
                filterChain.doFilter(request, response);
                return;
            }

            // Validate the token
            if (!tokenService.validateJwtToken(jwt)) {
                log.warn("Invalid JWT token");
                filterChain.doFilter(request, response);
                return;
            }

            // Check if this is an access token (not a refresh token)
            String tokenType = jwtUtils.getTokenTypeFromToken(jwt);
            if (!JwtUtils.TOKEN_TYPE_ACCESS.equals(tokenType)) {
                log.warn("Invalid token type for authentication: {}", tokenType);
                filterChain.doFilter(request, response);
                return;
            }

            // Get user identity and set it on the spring security context
            String username = tokenService.getUserNameFromJwtToken(jwt);
            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                
                // Validate token against user details
                if (tokenService.validateJwtToken(jwt)) {
                    UsernamePasswordAuthenticationToken authentication = 
                        new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities()
                        );
                    authentication.setDetails(
                        new WebAuthenticationDetailsSource().buildDetails(request)
                    );
                    
                    // Set the authentication in the security context
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    
                    // Add security headers to the response
                    addSecurityHeaders(response);
                }
            }
        } catch (Exception e) {
            log.error("Cannot set user authentication: {}", e.getMessage());
            // Don't throw exception here, just log it and continue the filter chain
            // The SecurityConfig will handle unauthorized requests
        }

        filterChain.doFilter(request, response);
    }
    
    /**
     * Check if the request is for a public endpoint.
     */
    private boolean isPublicEndpoint(HttpServletRequest request) {
        String path = request.getRequestURI();
        String method = request.getMethod();

        // Debug logging
        log.debug("Checking public endpoint: {} {}", method, path);

        // Don't bypass authentication for logout endpoint
        if (path.startsWith("/api/v1/auth/logout") && "POST".equalsIgnoreCase(method)) {
            return false;
        }

        // Public auth endpoints
        if (path.startsWith("/api/v1/auth/") ||
            path.startsWith("/v3/api-docs") ||
            path.startsWith("/swagger-ui") ||
            path.startsWith("/swagger-ui.html") ||
            path.startsWith("/api-docs") ||
            path.equals("/") ||
            path.startsWith("/api/v1/test/public") ||
            path.startsWith("/actuator/health") ||
            path.startsWith("/uploads/")) {
            return true;
        }

        // Public content endpoints (GET only)
        if ("GET".equalsIgnoreCase(method)) {
            boolean isPublic = path.equals("/api/v1/content") ||
                   path.matches("/api/v1/content/\\d+") ||
                   path.matches("/api/v1/content/user/[^/]+") ||
                   path.startsWith("/api/v1/content/search") ||
                   path.equals("/api/v1/content/trending") ||
                   path.equals("/api/v1/content/categories") ||
                   path.matches("/api/v1/content/category/[^/]+") ||
                   path.matches("/api/v1/content/\\d+/likes/count") ||
                   path.matches("/api/v1/content/\\d+/comments");

            log.debug("Content endpoint check: {} -> {}", path, isPublic);
            return isPublic;
        }

        log.debug("Not a public endpoint: {} {}", method, path);
        return false;
    }
    
    /**
     * Add security headers to the response.
     */
    private void addSecurityHeaders(HttpServletResponse response) {
        // Add security headers
        response.setHeader("X-Content-Type-Options", "nosniff");
        response.setHeader("X-XSS-Protection", "1; mode=block");
        response.setHeader("X-Frame-Options", "DENY");
        response.setHeader("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
        response.setHeader("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';");
    }

    private String parseJwt(HttpServletRequest request) {
        String headerAuth = request.getHeader(AUTH_HEADER);
        
        if (StringUtils.hasText(headerAuth) && headerAuth.startsWith(TOKEN_PREFIX)) {
            return headerAuth.substring(TOKEN_PREFIX.length());
        }
        
        return null;
    }
}
