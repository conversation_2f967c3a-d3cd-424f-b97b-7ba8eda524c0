package com.reelsnack.reelsnack.security.exception;

import org.springframework.security.core.AuthenticationException;

/**
 * Exception thrown when a token is invalid or expired.
 */
public class InvalidTokenException extends AuthenticationException {
    
    public InvalidTokenException(String message) {
        super(message);
    }
    
    public InvalidTokenException(String message, Throwable cause) {
        super(message, cause);
    }
}
