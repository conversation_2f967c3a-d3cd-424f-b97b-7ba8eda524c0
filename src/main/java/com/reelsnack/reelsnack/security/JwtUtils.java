package com.reelsnack.reelsnack.security;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Utility class for JWT token operations.
 */
@Slf4j
@Component
public class JwtUtils {
    public static final String TOKEN_TYPE = "Bearer";
    public static final String TOKEN_ISSUER = "reelsnack-api";
    public static final String TOKEN_AUDIENCE = "reelsnack-client";
    public static final String TOKEN_ROLE_CLAIM = "roles";
    public static final String TOKEN_TYPE_CLAIM = "type";
    public static final String TOKEN_TYPE_ACCESS = "access";
    public static final String TOKEN_TYPE_REFRESH = "refresh";

    @Value("${spring.jwt.secret}")
    private String jwtSecret;

    @Value("${spring.jwt.access-token-expiration-ms}")
    private long accessTokenExpirationMs;

    @Value("${spring.jwt.refresh-token-expiration-ms}")
    private long refreshTokenExpirationMs;
    
    // Package-private setters for testing
    void setJwtSecret(String jwtSecret) {
        this.jwtSecret = jwtSecret;
    }
    
    void setAccessTokenExpirationMs(long accessTokenExpirationMs) {
        this.accessTokenExpirationMs = accessTokenExpirationMs;
    }
    
    void setRefreshTokenExpirationMs(long refreshTokenExpirationMs) {
        this.refreshTokenExpirationMs = refreshTokenExpirationMs;
    }
    
    /**
     * Get the JWT token expiration time in milliseconds.
     *
     * @return the token expiration time in milliseconds
     */
    public long getJwtExpirationMs() {
        return accessTokenExpirationMs;
    }

    /**
     * Extracts the username from the JWT token.
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * Extracts the expiration date from the JWT token.
     */
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * Extracts a specific claim from the JWT token.
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * Extracts all claims from the JWT token.
     */
    private Claims extractAllClaims(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * Generates an access token for the given authentication.
     */
    public String generateAccessToken(Authentication authentication) {
        return generateToken(authentication, TOKEN_TYPE_ACCESS, accessTokenExpirationMs);
    }

    /**
     * Generates a refresh token for the given authentication.
     */
    public String generateRefreshToken(Authentication authentication) {
        return generateToken(authentication, TOKEN_TYPE_REFRESH, refreshTokenExpirationMs);
    }

    /**
     * Generates a JWT token with the specified type and expiration.
     */
    private String generateToken(Authentication authentication, String tokenType, long expirationMs) {
        UserDetails userPrincipal = (UserDetails) authentication.getPrincipal();
        
        Map<String, Object> claims = new HashMap<>();
        claims.put(TOKEN_TYPE_CLAIM, tokenType);
        claims.put(TOKEN_ROLE_CLAIM, userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList()));

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(userPrincipal.getUsername())
                .setIssuer(TOKEN_ISSUER)
                .setAudience(TOKEN_AUDIENCE)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expirationMs))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }



    /**
     * Validates a JWT token.
     *
     * @param token The JWT token to validate
     * @return true if the token is valid, false otherwise
     */
    public boolean validateJwtToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("JWT token is null or empty");
            return false;
        }

        try {
            // Parse the token to verify its signature and expiration
            Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

            // Additional validation: Check if the token has expired
            Date expiration = claims.getExpiration();
            if (expiration.before(new Date())) {
                log.warn("JWT token has expired: {}", expiration);
                return false;
            }

            // Validate required claims
            if (claims.getSubject() == null || claims.getSubject().trim().isEmpty()) {
                log.warn("JWT token has no subject");
                return false;
            }

            // Validate token type if present
            String tokenType = claims.get(TOKEN_TYPE_CLAIM, String.class);
            if (tokenType != null && !tokenType.isEmpty()) {
                if (!TOKEN_TYPE_ACCESS.equals(tokenType) && !TOKEN_TYPE_REFRESH.equals(tokenType)) {
                    log.warn("Invalid token type: {}", tokenType);
                    return false;
                }
            }

            return true;

        } catch (SignatureException e) {
            log.error("Invalid JWT signature: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT claims string is empty: {}", e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error validating JWT token: {}", e.getMessage());
        }
        return false;
    }

    /**
     * Validates a JWT token against user details.
     */
    public boolean validateJwtToken(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    /**
     * Checks if a token is expired.
     */
    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * Gets the signing key for JWT.
     */
    private Key getSigningKey() {
        byte[] keyBytes = jwtSecret.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * Gets the token type from a JWT token.
     */
    public String getTokenTypeFromToken(String token) {
        return extractClaim(token, claims -> claims.get(TOKEN_TYPE_CLAIM, String.class));
    }

    /**
     * Gets the roles from a JWT token.
     */
    @SuppressWarnings("unchecked")
    public List<String> getRolesFromToken(String token) {
        Claims claims = extractAllClaims(token);
        List<String> roles = claims.get(TOKEN_ROLE_CLAIM, List.class);
        return roles != null ? roles : new ArrayList<>();
    }
    
    /**
     * Generates a JWT token from username and token type.
     *
     * @param username The username
     * @param tokenType The type of token (access or refresh)
     * @return The JWT token
     */
    public String generateTokenFromUsername(String username, String tokenType) {
        long expirationMs = TOKEN_TYPE_ACCESS.equals(tokenType) ? accessTokenExpirationMs : refreshTokenExpirationMs;
        
        return Jwts.builder()
                .setSubject(username)
                .setIssuer(TOKEN_ISSUER)
                .setAudience(TOKEN_AUDIENCE)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expirationMs))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .claim(TOKEN_TYPE_CLAIM, tokenType)
                .compact();
    }

    /**
     * Extracts the user ID from a JWT token.
     *
     * @param token The JWT token
     * @return The user ID or null if not found
     */
    public Long getUserIdFromToken(String token) {
        try {
            Claims claims = extractAllClaims(token);
            return claims.get("userId", Long.class);
        } catch (Exception e) {
            log.error("Error extracting user ID from token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Gets the access token expiration time in milliseconds.
     * @return The access token expiration time in milliseconds
     */
    public long getAccessTokenExpirationMs() {
        return accessTokenExpirationMs;
    }
    
    /**
     * Gets the refresh token expiration time in milliseconds.
     * @return The refresh token expiration time in milliseconds
     */
    public long getRefreshTokenExpirationMs() {
        return refreshTokenExpirationMs;
    }
}
