package com.reelsnack.reelsnack.security;

import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.reelsnack.reelsnack.exception.TokenValidationException;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.security.TokenPair;
import com.reelsnack.reelsnack.repository.UserRepository;

/**
 * Service for managing JWT tokens and refresh tokens.
 */
@Slf4j
@Service
@AllArgsConstructor
public class TokenService {
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";
    
    private final JwtUtils jwtUtils;
    private final RedisTemplate<String, String> redisTemplate;
    private final UserRepository userRepository;

    /**
     * Generates a new access token and refresh token pair.
     *
     * @param authentication The authentication object containing user details
     * @return TokenPair containing access token and refresh token
     */
    @Transactional
    public TokenPair generateTokenPair(Authentication authentication) {
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String username = userDetails.getUsername();
        
        // Generate tokens
        String accessToken = jwtUtils.generateAccessToken(authentication);
        String refreshToken = jwtUtils.generateRefreshToken(authentication);
        
        // Store refresh token in Redis with TTL
        storeRefreshToken(username, refreshToken);
        
        // Get the expiration time for the access token
        long expiresIn = jwtUtils.getAccessTokenExpirationMs() / 1000; // Convert to seconds
        
        return TokenPair.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType(JwtUtils.TOKEN_TYPE)
                .expiresIn(expiresIn)
                .build();
    }

    /**
     * Refreshes an access token using a valid refresh token.
     *
     * @param refreshToken The refresh token
     * @return New TokenPair if refresh is successful
     */
    @Transactional
    public TokenPair refreshToken(String refreshToken) {
        // Validate the refresh token
        if (!jwtUtils.validateJwtToken(refreshToken)) {
            throw new TokenValidationException("Invalid refresh token");
        }

        // Verify the token type is refresh
        String tokenType = jwtUtils.getTokenTypeFromToken(refreshToken);
        if (!JwtUtils.TOKEN_TYPE_REFRESH.equals(tokenType)) {
            throw new TokenValidationException("Invalid token type. Refresh token expected.");
        }

        String username = jwtUtils.extractUsername(refreshToken);
        String storedRefreshToken = getStoredRefreshToken(username);
        
        // Check if the refresh token exists and matches the stored one
        if (storedRefreshToken == null) {
            throw new TokenValidationException("Refresh token not found. Please log in again.");
        }
        
        if (!storedRefreshToken.equals(refreshToken)) {
            // Possible token reuse! Revoke all tokens for this user as a security measure
            revokeRefreshToken(username);
            throw new TokenValidationException("Invalid refresh token. Possible token reuse detected. Please log in again.");
        }
        
        // Generate new tokens
        String newAccessToken = jwtUtils.generateTokenFromUsername(username, JwtUtils.TOKEN_TYPE_ACCESS);
        String newRefreshToken = jwtUtils.generateTokenFromUsername(username, JwtUtils.TOKEN_TYPE_REFRESH);
        
        // Delete old refresh token and store the new one
        redisTemplate.delete(getRefreshTokenKey(username));
        storeRefreshToken(username, newRefreshToken);
        
        // Get the expiration time for the access token
        long expiresIn = jwtUtils.getAccessTokenExpirationMs() / 1000; // Convert to seconds
        
        // Log the refresh operation
        log.info("Refreshed tokens for user: {}", username);
        
        return TokenPair.builder()
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)
                .tokenType(JwtUtils.TOKEN_TYPE)
                .expiresIn(expiresIn)
                .build();
    }

    /**
     * Validates an access token.
     *
     * @param token The JWT token to validate
     * @return true if the token is valid, false otherwise
     */
    public boolean validateAccessToken(String token) {
        if (!jwtUtils.validateJwtToken(token)) {
            return false;
        }
        
        String tokenType = jwtUtils.getTokenTypeFromToken(token);
        return JwtUtils.TOKEN_TYPE_ACCESS.equals(tokenType);
    }

    /**
     * Gets the username from a token.
     *
     * @param token The JWT token
     * @return The username
     */
    public String getUsernameFromToken(String token) {
        return jwtUtils.extractUsername(token);
    }
    
    /**
     * Validates a JWT token.
     *
     * @param token The JWT token to validate
     * @return true if the token is valid, false otherwise
     */
    public boolean validateJwtToken(String token) {
        return jwtUtils.validateJwtToken(token);
    }
    
    /**
     * Gets the username from a JWT token.
     * This is an alias for getUsernameFromToken for backward compatibility.
     *
     * @param token The JWT token
     * @return The username
     */
    public String getUserNameFromJwtToken(String token) {
        return getUsernameFromToken(token);
    }

    /**
     * Gets the roles from a token.
     *
     * @param token The JWT token
     * @return List of role names
     */
    public List<String> getRolesFromToken(String token) {
        return jwtUtils.getRolesFromToken(token);
    }

    /**
     * Gets the user ID from a token.
     *
     * @param token The JWT token
     * @return The user ID or null if not found
     */
    public Long getUserIdFromToken(String token) {
        return jwtUtils.getUserIdFromToken(token);
    }

    /**
     * Stores a refresh token in Redis.
     *
     * @param username The username
     * @param refreshToken The refresh token
     */
    private void storeRefreshToken(String username, String refreshToken) {
        String key = getRefreshTokenKey(username);
        long expirationMs = jwtUtils.getRefreshTokenExpirationMs();
        
        redisTemplate.opsForValue().set(
            key, 
            refreshToken, 
            expirationMs, 
            TimeUnit.MILLISECONDS
        );
        log.debug("Stored refresh token for user: {}", username);
    }

    /**
     * Gets a stored refresh token from Redis.
     *
     * @param username The username
     * @return The refresh token or null if not found
     */
    private String getStoredRefreshToken(String username) {
        return redisTemplate.opsForValue().get(getRefreshTokenKey(username));
    }

    /**
     * Revokes a refresh token.
     *
     * @param username The username
     */
    public void revokeRefreshToken(String username) {
        if (username != null) {
            redisTemplate.delete(getRefreshTokenKey(username));
            log.debug("Revoked refresh token for user: {}", username);
        }
    }

    /**
     * Gets the Redis key for a user's refresh token.
     *
     * @param username The username
     * @return The Redis key
     */
    private String getRefreshTokenKey(String username) {
        return REFRESH_TOKEN_PREFIX + username;
    }

    /**
     * Gets the remaining time for a token in seconds.
     *
     * @param token The JWT token
     * @return Remaining time in seconds, or -1 if token is invalid
     */
    public long getRemainingTimeInSeconds(String token) {
        try {
            Date expiration = jwtUtils.extractExpiration(token);
            if (expiration == null) {
                return -1;
            }
            long remainingMs = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0, remainingMs / 1000);
        } catch (Exception e) {
            log.error("Error getting remaining time for token: {}", e.getMessage());
            return -1;
        }
    }
    
    /**
     * Generates and saves a verification token for the given user.
     *
     * @param user The user to generate the token for
     * @return The generated token
     */
    public String generateAndSaveVerificationToken(User user) {
        String token = jwtUtils.generateTokenFromUsername(user.getUsername(), "verification");
        String key = "verification_token:" + user.getUsername();
        long expirationMs = 24 * 60 * 60 * 1000; // 24 hours
        
        redisTemplate.opsForValue().set(
            key,
            token,
            expirationMs,
            TimeUnit.MILLISECONDS
        );
        
        return token;
    }
    
    /**
     * Validates a verification token.
     *
     * @param token The verification token
     * @return The user if the token is valid
     */
    public User validateVerificationToken(String token) {
        if (!jwtUtils.validateJwtToken(token)) {
            throw new TokenValidationException("Invalid or expired verification token");
        }
        
        String username = jwtUtils.extractUsername(token);
        String key = "verification_token:" + username;
        String storedToken = redisTemplate.opsForValue().get(key);
        
        if (storedToken == null || !storedToken.equals(token)) {
            throw new TokenValidationException("Invalid or expired verification token");
        }
        
        return userRepository.findByUsername(username)
            .orElseThrow(() -> new TokenValidationException("User not found"));
    }
    
    /**
     * Deletes a verification token.
     *
     * @param token The verification token to delete
     */
    public void deleteVerificationToken(String token) {
        try {
            String username = jwtUtils.extractUsername(token);
            String key = "verification_token:" + username;
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Error deleting verification token: {}", e.getMessage());
        }
    }
    
    /**
     * Revokes a token for the given username.
     * This is an alias for revokeRefreshToken for backward compatibility.
     *
     * @param username The username
     */
    public void revokeToken(String username) {
        revokeRefreshToken(username);
    }
    
    /**
     * Generates and saves a password reset token for the given user.
     *
     * @param user The user to generate the token for
     * @return The generated token
     */
    public String generateAndSavePasswordResetToken(User user) {
        String token = jwtUtils.generateTokenFromUsername(user.getUsername(), "password_reset");
        String key = "password_reset_token:" + user.getUsername();
        long expirationMs = 24 * 60 * 60 * 1000; // 24 hours
        
        redisTemplate.opsForValue().set(
            key,
            token,
            expirationMs,
            TimeUnit.MILLISECONDS
        );
        
        return token;
    }
    
    /**
     * Validates a password reset token.
     *
     * @param token The password reset token
     * @return The user if the token is valid
     */
    public User validatePasswordResetToken(String token) {
        if (!jwtUtils.validateJwtToken(token)) {
            throw new TokenValidationException("Invalid or expired password reset token");
        }
        
        String username = jwtUtils.extractUsername(token);
        String key = "password_reset_token:" + username;
        String storedToken = redisTemplate.opsForValue().get(key);
        
        if (storedToken == null || !storedToken.equals(token)) {
            throw new TokenValidationException("Invalid or expired password reset token");
        }
        
        return userRepository.findByUsername(username)
            .orElseThrow(() -> new TokenValidationException("User not found"));
    }
    
    /**
     * Deletes a password reset token.
     *
     * @param user The user whose password reset token should be deleted
     */
    public void deletePasswordResetToken(User user) {
        try {
            String key = "password_reset_token:" + user.getUsername();
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Error deleting password reset token: {}", e.getMessage());
        }
    }
}
