# Test configuration
spring:
  # Configure H2 in-memory database for testing
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL
    driver-class-name: org.h2.Driver
    username: sa
    password: password
    initialization-mode: always
    platform: h2
    
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
        hbm2ddl:
          auto: update
    
  # Disable Flyway in tests by default
  flyway:
    enabled: false
    
  # Enable JPA repositories
  data:
    jpa:
      repositories:
        enabled: true

# Test logging configuration
logging:
  level:
    root: INFO
    com.reelsnack.reelsnack: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Test server configuration
server:
  port: 0  # Use random available port for tests
  servlet:
    context-path: /api/v1

# Test CORS configuration
cors:
  allowed-origins: "http://localhost:3000,http://localhost:8080"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  exposed-headers: "Authorization,Content-Disposition"
  max-age: 3600

# Test file upload configuration
file:
  upload-dir: uploads/test
  max-file-size: 5MB
  max-request-size: 10MB

# Test email configuration (mock)
spring.mail.host: localhost
spring.mail.port: 2525
spring.mail.username: <EMAIL>
spring.mail.password: test123
spring.mail.properties.mail.smtp.auth: true
spring.mail.properties.mail.smtp.starttls.enable: true

# Test cache configuration
spring.cache.type: none

# Test actuator endpoints
management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true
  health:
    db:
      enabled: true
    diskSpace:
      enabled: true
    ping:
      enabled: true

# Test security configuration
security:
  oauth2:
    client:
      registration:
        google:
          client-id: test-google-client-id
          client-secret: test-google-client-secret
        facebook:
          client-id: test-facebook-client-id
          client-secret: test-facebook-client-secret
  jwt:
    secret: test-jwt-secret-key-1234567890abcdefghijklmnopqrstuvwxyz
    expiration: 86400000
    refresh-expiration: 604800000

# Test application properties
app:
  frontend-url: http://localhost:3000
  backend-url: http://localhost:8080
  allowed-origins: "http://localhost:3000,http://localhost:8080"
  file-storage:
    upload-dir: uploads/test
    max-file-size: 5MB
  email:
    from: <EMAIL>
    reply-to: <EMAIL>
    admin-email: <EMAIL>
  security:
    authentication:
      jwt:
        secret: test-jwt-secret-key-1234567890abcdefghijklmnopqrstuvwxyz
        expiration-ms: 86400000
        refresh-expiration-ms: 604800000
    password:
      strength: 10
      min-length: 6
    cors:
      allowed-origins: "http://localhost:3000,http://localhost:8080"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
      allowed-headers: "*"
      exposed-headers: "Authorization,Content-Disposition"
      max-age: 3600
