package com.reelsnack.reelsnack.integration;

import com.reelsnack.reelsnack.dto.TokenPair;
import com.reelsnack.reelsnack.dto.request.LoginRequest;
import com.reelsnack.reelsnack.dto.request.SignupRequest;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.repository.UserRepository;
import com.reelsnack.reelsnack.ReelSnackApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.http.*;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = ReelSnackApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:application-test.yml")
@Import({com.reelsnack.reelsnack.config.TestSecurityConfiguration.class, com.reelsnack.reelsnack.config.TestConfig.class})
public class AuthIntegrationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private String baseUrl;

    @BeforeEach
    void setUp() {
        baseUrl = "http://localhost:" + port + "/api/v1/auth";
        userRepository.deleteAll();
    }

    @Test
    void registerAndLogin_ShouldReturnTokens() {
        // Register a new user
        SignupRequest signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPassword("password123");
        signupRequest.setRoles(Collections.singleton("user"));

        ResponseEntity<Void> signupResponse = restTemplate.postForEntity(
            baseUrl + "/signup",
            signupRequest,
            Void.class
        );
        assertEquals(HttpStatus.OK, signupResponse.getStatusCode());

        // Login with the new user
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsernameOrEmail("testuser");
        loginRequest.setPassword("password123");

        ResponseEntity<TokenPair> loginResponse = restTemplate.postForEntity(
            baseUrl + "/signin",
            loginRequest,
            TokenPair.class
        );

        // Verify login was successful
        assertEquals(HttpStatus.OK, loginResponse.getStatusCode());
        assertNotNull(loginResponse.getBody());
        assertNotNull(loginResponse.getBody().getAccessToken());
        assertNotNull(loginResponse.getBody().getRefreshToken());
    }

    @Test
    void refreshToken_ShouldReturnNewTokens() {
        // Create a test user
        User user = new User();
        user.setUsername("refreshuser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setEnabled(true);
        userRepository.save(user);

        // Login to get tokens
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsernameOrEmail("refreshuser");
        loginRequest.setPassword("password123");

        ResponseEntity<TokenPair> loginResponse = restTemplate.postForEntity(
            baseUrl + "/signin",
            loginRequest,
            TokenPair.class
        );

        TokenPair tokens = loginResponse.getBody();
        assertNotNull(tokens);
        assertNotNull(tokens.getRefreshToken());

        // Refresh the token
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(tokens.getAccessToken());

        String refreshTokenJson = "{\"refreshToken\":\"" + tokens.getRefreshToken() + "\"}";
        HttpEntity<String> request = new HttpEntity<>(refreshTokenJson, headers);

        ResponseEntity<TokenPair> refreshResponse = restTemplate.exchange(
            baseUrl + "/refresh-token",
            HttpMethod.POST,
            request,
            TokenPair.class
        );

        // Verify refresh was successful
        assertEquals(HttpStatus.OK, refreshResponse.getStatusCode());
        assertNotNull(refreshResponse.getBody());
        assertNotNull(refreshResponse.getBody().getAccessToken());
        assertNotNull(refreshResponse.getBody().getRefreshToken());
    }

    @Test
    void accessProtectedResource_WithValidToken_ShouldSucceed() {
        // Create a test user
        User user = new User();
        user.setUsername("protecteduser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setEnabled(true);
        userRepository.save(user);

        // Login to get tokens
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsernameOrEmail("protecteduser");
        loginRequest.setPassword("password123");

        ResponseEntity<TokenPair> loginResponse = restTemplate.postForEntity(
            baseUrl + "/signin",
            loginRequest,
            TokenPair.class
        );

        TokenPair tokens = loginResponse.getBody();
        assertNotNull(tokens);

        // Access a protected resource
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(tokens.getAccessToken());
        HttpEntity<?> entity = new HttpEntity<>(headers);

        // Replace "/api/v1/users/me" with an actual protected endpoint in your application
        ResponseEntity<String> response = restTemplate.exchange(
            "http://localhost:" + port + "/api/v1/users/me",
            HttpMethod.GET,
            entity,
            String.class
        );

        // The actual status might be 404 if the endpoint doesn't exist,
        // but the important part is that it's not 401 Unauthorized
        assertNotEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    void logout_ShouldRevokeToken() {
        // Create a test user
        User user = new User();
        user.setUsername("logoutuser");
        user.setEmail("<EMAIL>");
        user.setPassword(passwordEncoder.encode("password123"));
        user.setEnabled(true);
        userRepository.save(user);

        // Login to get tokens
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsernameOrEmail("logoutuser");
        loginRequest.setPassword("password123");

        ResponseEntity<TokenPair> loginResponse = restTemplate.postForEntity(
            baseUrl + "/signin",
            loginRequest,
            TokenPair.class
        );

        TokenPair tokens = loginResponse.getBody();
        assertNotNull(tokens);

        // Logout
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(tokens.getAccessToken());
        HttpEntity<?> entity = new HttpEntity<>(headers);

        ResponseEntity<Void> logoutResponse = restTemplate.exchange(
            baseUrl + "/logout",
            HttpMethod.POST,
            entity,
            Void.class
        );

        assertEquals(HttpStatus.NO_CONTENT, logoutResponse.getStatusCode());

        // Try to access a protected resource with the old token
        // This should fail because the token should be invalidated
        ResponseEntity<String> response = restTemplate.exchange(
            "http://localhost:" + port + "/api/v1/users/me",
            HttpMethod.GET,
            entity,
            String.class
        );

        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }
}
