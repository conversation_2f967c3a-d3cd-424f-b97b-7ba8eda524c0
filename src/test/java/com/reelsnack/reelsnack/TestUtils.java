package com.reelsnack.reelsnack;

import com.reelsnack.reelsnack.dto.request.ChangePasswordRequest;
import com.reelsnack.reelsnack.dto.request.SignupRequest;
import com.reelsnack.reelsnack.dto.request.UpdateProfileRequest;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserPreference;
import com.reelsnack.reelsnack.model.enums.Role;

import java.util.HashSet;
import java.util.Set;

/**
 * Utility class for test data creation and common test methods.
 */
public class TestUtils {

    // Test user constants
    public static final String TEST_USERNAME = "testuser";
    public static final String TEST_EMAIL = "<EMAIL>";
    public static final String TEST_PASSWORD = "password123";
    public static final String TEST_FIRST_NAME = "Test";
    public static final String TEST_LAST_NAME = "User";
    public static final String TEST_PHONE = "+1234567890";
    public static final String TEST_BIO = "Test bio";
    public static final String TEST_PROFILE_PIC = "test.jpg";

    /**
     * Creates a test user with the specified role.
     *
     * @param role The role to assign to the user
     * @return A User object with test data
     */
    public static User createTestUser(Role role) {
        return User.builder()
                .username(TEST_USERNAME)
                .email(TEST_EMAIL)
                .password(TEST_PASSWORD)
                .firstName(TEST_FIRST_NAME)
                .lastName(TEST_LAST_NAME)
                .phoneNumber(TEST_PHONE)
                .profilePicture(TEST_PROFILE_PIC)
                .bio(TEST_BIO)
                .role(role)
                .enabled(true)
                .build();
    }

    /**
     * Creates a test user preference.
     *
     * @param user The user to associate with the preferences
     * @return A UserPreference object with test data
     */
    public static UserPreference createTestUserPreference(User user) {
        Set<String> dietaryRestrictions = new HashSet<>();
        dietaryRestrictions.add("VEGETARIAN");
        dietaryRestrictions.add("GLUTEN_FREE");

        Set<String> preferredCuisines = new HashSet<>();
        preferredCuisines.add("ITALIAN");
        preferredCuisines.add("MEXICAN");

        return UserPreference.builder()
                .user(user)
                .emailNotifications(true)
                .pushNotifications(true)
                .smsNotifications(false)
                .language("en")
                .theme("light")
                .dietaryRestrictions(dietaryRestrictions)
                .preferredCuisines(preferredCuisines)
                .build();
    }

    /**
     * Creates a test signup request.
     *
     * @return A SignupRequest object with test data
     */
    public static SignupRequest createSignupRequest() {
        SignupRequest request = new SignupRequest();
        request.setUsername(TEST_USERNAME);
        request.setEmail(TEST_EMAIL);
        request.setPassword(TEST_PASSWORD);
        request.setFirstName(TEST_FIRST_NAME);
        request.setLastName(TEST_LAST_NAME);
        request.setPhoneNumber(TEST_PHONE);
        return request;
    }

    /**
     * Creates a test update profile request.
     *
     * @return An UpdateProfileRequest object with test data
     */
    public static UpdateProfileRequest createUpdateProfileRequest() {
        UpdateProfileRequest request = new UpdateProfileRequest();
        request.setFirstName("Updated" + TEST_FIRST_NAME);
        request.setLastName("Updated" + TEST_LAST_NAME);
        request.setPhoneNumber("+9876543210");
        request.setProfilePicture("updated.jpg");
        request.setBio("Updated " + TEST_BIO);
        return request;
    }

    /**
     * Creates a test change password request.
     *
     * @return A ChangePasswordRequest object with test data
     */
    public static ChangePasswordRequest createChangePasswordRequest() {
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("old" + TEST_PASSWORD);
        request.setNewPassword("new" + TEST_PASSWORD);
        request.setConfirmNewPassword("new" + TEST_PASSWORD);
        return request;
    }
}
