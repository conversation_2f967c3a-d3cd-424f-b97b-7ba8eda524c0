package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.SignupRequest;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.mapper.UserMapper;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.UserRepository;
import com.reelsnack.reelsnack.security.JwtUtils;
import com.reelsnack.reelsnack.security.TokenService;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.security.UserDetailsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtUtils jwtUtils;

    @Mock
    private UserDetailsServiceImpl userDetailsService;
    
    @Mock
    private UserMapper userMapper;
    
    @Mock
    private TokenService tokenService;
    
    @Mock
    private EmailService emailService;

    @InjectMocks
    private AuthService authService;

    private SignupRequest signupRequest;

    @BeforeEach
    void setUp() {
        signupRequest = new SignupRequest();
        signupRequest.setUsername("testuser");
        signupRequest.setEmail("<EMAIL>");
        signupRequest.setPassword("password123");
        signupRequest.setPhoneNumber("+1234567890");
        signupRequest.setFirstName("Test");
        signupRequest.setLastName("User");
    }

    @Test
    void registerUser_WithNewUser_ShouldSaveUserWithDefaultRole() {
        // Arrange
        User user = new User();
        user.setUsername(signupRequest.getUsername());
        user.setEmail(signupRequest.getEmail());
        user.setPassword("encodedPassword");
        user.setPhoneNumber(signupRequest.getPhoneNumber());
        user.setFirstName(signupRequest.getFirstName());
        user.setLastName(signupRequest.getLastName());
        user.setEnabled(false);
        
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(userMapper.toEntity(any(SignupRequest.class))).thenReturn(user);
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(tokenService.generateAndSaveVerificationToken(any(User.class))).thenReturn("test-token");
        doNothing().when(emailService).sendVerificationEmail(anyString(), anyString(), anyString());

        // Act
        authService.registerUser(signupRequest);

        // Assert
        ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
        verify(userRepository).save(userCaptor.capture());
        
        User savedUser = userCaptor.getValue();
        assertNotNull(savedUser);
        assertEquals(signupRequest.getUsername(), savedUser.getUsername());
        assertEquals(signupRequest.getEmail(), savedUser.getEmail());
        assertEquals("encodedPassword", savedUser.getPassword());
        assertEquals(signupRequest.getPhoneNumber(), savedUser.getPhoneNumber());
        assertEquals(signupRequest.getFirstName(), savedUser.getFirstName());
        assertEquals(signupRequest.getLastName(), savedUser.getLastName());
        assertFalse(savedUser.isEnabled(), "User should be disabled until email is verified");
        verify(userRepository).save(any(User.class));
    }

    @Test
    void registerUser_WithExistingUsername_ShouldThrowException() {
        // Arrange
        when(userRepository.existsByUsername(anyString())).thenReturn(true);

        // Act & Assert
        assertThrows(BadRequestException.class, () -> authService.registerUser(signupRequest));
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void registerUser_WithExistingEmail_ShouldThrowException() {
        // Arrange
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        // Act & Assert
        assertThrows(BadRequestException.class, () -> authService.registerUser(signupRequest));
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void registerUser_WithSpecificRole_ShouldSaveUserWithRole() {
        // Arrange
        signupRequest.setRoles(Set.of("admin"));
        
        User user = new User();
        user.setUsername(signupRequest.getUsername());
        user.setEmail(signupRequest.getEmail());
        user.setPassword("encodedPassword");
        user.setRole(Role.ROLE_ADMIN);
        user.setEnabled(false);
        
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(userMapper.toEntity(any(SignupRequest.class))).thenReturn(user);
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(tokenService.generateAndSaveVerificationToken(any(User.class))).thenReturn("test-token");
        doNothing().when(emailService).sendVerificationEmail(anyString(), anyString(), anyString());

        // Act
        authService.registerUser(signupRequest);

        // Assert
        ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
        verify(userRepository).save(userCaptor.capture());
        
        User savedUser = userCaptor.getValue();
        assertNotNull(savedUser);
        assertEquals(Role.ROLE_ADMIN, savedUser.getRole());
    }
}
