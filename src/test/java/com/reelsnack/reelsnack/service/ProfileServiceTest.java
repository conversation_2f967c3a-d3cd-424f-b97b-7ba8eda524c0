package com.reelsnack.reelsnack.service;

import com.reelsnack.reelsnack.dto.request.ChangePasswordRequest;
import com.reelsnack.reelsnack.dto.request.UpdateProfileRequest;
import com.reelsnack.reelsnack.dto.response.ProfileResponse;
import com.reelsnack.reelsnack.exception.BadRequestException;
import com.reelsnack.reelsnack.exception.ResourceNotFoundException;
import com.reelsnack.reelsnack.model.User;
import com.reelsnack.reelsnack.model.UserPreference;
import com.reelsnack.reelsnack.model.enums.Role;
import com.reelsnack.reelsnack.repository.UserPreferenceRepository;
import com.reelsnack.reelsnack.repository.UserRepository;
import com.reelsnack.reelsnack.security.UserDetailsImpl;
import com.reelsnack.reelsnack.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProfileServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserPreferenceRepository userPreferenceRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private ProfileService profileService;

    private User testUser;
    private UserDetailsImpl userDetails;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .password("encodedPassword")
                .role(Role.ROLE_USER)
                .enabled(true)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        userDetails = UserDetailsImpl.build(testUser);
    }

    @Test
    void getCurrentUserProfile_UserExists_ReturnsProfile() {
        // Arrange
        String username = "testuser";
        User user = new User();
        user.setId(1L);
        user.setUsername(username);
        user.setEmail("<EMAIL>");
        
        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        when(userPreferenceRepository.findByUser(testUser)).thenReturn(Optional.of(UserPreference.builder().user(testUser).build()));
        when(userMapper.toProfileResponse(testUser, UserPreference.builder().user(testUser).build())).thenReturn(ProfileResponse.builder().username(testUser.getUsername()).email(testUser.getEmail()).build());

        // Act
        ProfileResponse response = profileService.getCurrentUserProfile(userDetails);

        // Assert
        assertNotNull(response);
        assertEquals(testUser.getUsername(), response.getUsername());
        assertEquals(testUser.getEmail(), response.getEmail());
        verify(userRepository, times(1)).findById(testUser.getId());
        verify(userMapper, times(1)).toProfileResponse(testUser, UserPreference.builder().user(testUser).build());
    }

    @Test
    void getCurrentUserProfile_UserNotFound_ThrowsException() {
        // Arrange
        when(userRepository.findById(testUser.getId())).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> 
            profileService.getCurrentUserProfile(userDetails));
        verify(userRepository, times(1)).findById(testUser.getId());
    }

    @Test
    void updateProfile_ValidRequest_UpdatesProfile() {
        // Arrange
        UpdateProfileRequest request = new UpdateProfileRequest();
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setPhoneNumber("+1234567890");
        request.setBio("Test bio");

        UserPreference preferences = UserPreference.builder()
                .user(testUser)
                .build();
                
        ProfileResponse expectedResponse = ProfileResponse.builder()
                .username(testUser.getUsername())
                .email(testUser.getEmail())
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .phoneNumber(request.getPhoneNumber())
                .bio(request.getBio())
                .build();

        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        when(userRepository.save(any(User.class))).thenAnswer(invocation -> {
            User savedUser = invocation.getArgument(0);
            testUser.setFirstName(savedUser.getFirstName());
            testUser.setLastName(savedUser.getLastName());
            testUser.setPhoneNumber(savedUser.getPhoneNumber());
            testUser.setBio(savedUser.getBio());
            return testUser;
        });
        when(userPreferenceRepository.findByUser(testUser)).thenReturn(Optional.of(preferences));
        when(userMapper.toProfileResponse(any(User.class), eq(preferences))).thenReturn(expectedResponse);

        // Act
        ProfileResponse response = profileService.updateProfile(userDetails, request);

        // Assert
        assertNotNull(response);
        assertEquals(request.getFirstName(), response.getFirstName());
        assertEquals(request.getLastName(), response.getLastName());
        assertEquals(request.getPhoneNumber(), response.getPhoneNumber());
        assertEquals(request.getBio(), response.getBio());
        verify(userRepository, times(1)).save(any(User.class));
        verify(userMapper, times(1)).toProfileResponse(any(User.class), eq(preferences));
    }

    @Test
    void changePassword_ValidRequest_UpdatesPassword() {
        // Arrange
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmNewPassword("newPassword");

        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(request.getCurrentPassword(), testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.encode(request.getNewPassword())).thenReturn("encodedNewPassword");

        // Act & Assert
        assertDoesNotThrow(() -> profileService.changePassword(userDetails, request));
        verify(userRepository, times(1)).save(any(User.class));
    }

    @Test
    void changePassword_IncorrectCurrentPassword_ThrowsException() {
        // Arrange
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("wrongPassword");
        request.setNewPassword("newPassword");
        request.setConfirmNewPassword("newPassword");

        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(request.getCurrentPassword(), testUser.getPassword())).thenReturn(false);

        // Act & Assert
        assertThrows(BadRequestException.class, () -> profileService.changePassword(userDetails, request));
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void changePassword_NewPasswordsDontMatch_ThrowsException() {
        // Arrange
        ChangePasswordRequest request = new ChangePasswordRequest();
        request.setCurrentPassword("oldPassword");
        request.setNewPassword("newPassword");
        request.setConfirmNewPassword("differentPassword");

        // Act & Assert
        assertThrows(BadRequestException.class, () -> profileService.changePassword(userDetails, request));
        verify(userRepository, never()).save(any(User.class));
    }
}
