package com.reelsnack.reelsnack;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Test configuration for the application tests.
 * Provides test-specific beans and configurations.
 */
@TestConfiguration
public class TestConfig {

    /**
     * Provides a password encoder for testing purposes.
     * Uses BCrypt with strength 10 for fast hashing during tests.
     *
     * @return A PasswordEncoder instance
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(10); // Lower strength for faster tests
    }
}
