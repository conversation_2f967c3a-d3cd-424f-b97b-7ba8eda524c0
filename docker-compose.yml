version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: reelsnack-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_URL=jdbc:postgresql://db:5432/${POSTGRES_DB:-reelsnack}
      - DB_USERNAME=${POSTGRES_USER:-postgres}
      - DB_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - JWT_SECRET=${JWT_SECRET:-your-256-bit-secret-key-32-characters-long}
      - JWT_EXPIRATION_MS=${JWT_EXPIRATION_MS:-86400000}
    depends_on:
      - db
    networks:
      - reelsnack-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:15
    container_name: reelsnack-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-reelsnack}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - reelsnack-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-reelsnack}"]
      interval: 5s
      timeout: 5s
      retries: 5

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: reelsnack-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD:-admin}
    ports:
      - "5050:80"
    depends_on:
      - db
    networks:
      - reelsnack-network

networks:
  reelsnack-network:
    driver: bridge

volumes:
  postgres_data:
