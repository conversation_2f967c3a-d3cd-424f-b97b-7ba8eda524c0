# 🍔 ReelSnack - Hyper-Social Food Delivery App

ReelSnack is a cutting-edge food delivery platform that combines the convenience of food ordering with the engagement of social media. Discover new restaurants through short-form videos, interact with food creators, and get your favorite meals delivered to your doorstep.

## ✨ Features

- 🔐 **Secure Authentication** - JWT-based authentication with role-based access control
- 🎥 **Video-First Discovery** - Browse restaurants through engaging short-form videos
- 🛍️ **Seamless Ordering** - Intuitive food ordering system with real-time tracking
- 🏪 **Vendor Dashboard** - Comprehensive tools for restaurant owners to manage their business
- 🤝 **Social Integration** - Follow foodies, like, comment, and share your food journey
- ⚡ **Real-Time Updates** - Instant notifications for order status and social interactions
- 📊 **Analytics** - Insights into customer behavior and sales performance
- 🌍 **Multi-Language Support** - Built-in internationalization for global reach

## 🛠 Tech Stack

- **Backend Framework**: Spring Boot 3.5.3
- **Database**: PostgreSQL 15
- **Authentication**: JWT (JSON Web Tokens)
- **API Documentation**: SpringDoc OpenAPI 3.0
- **Build Tool**: Maven
- **Java Version**: 17
- **Containerization**: Docker
- **CI/CD**: GitHub Actions

## 🚀 Getting Started

### Prerequisites

- Java 17 or later
- Maven 3.9.0 or later
- PostgreSQL 15 or later
- Docker (optional, for containerized deployment)

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/reelsnack.git
   cd reelsnack
   ```

2. **Database Setup**
   - Install PostgreSQL and create a new database named `reelsnack`
   - Or use Docker to run PostgreSQL:
     ```bash
     docker run --name reelsnack-db -e POSTGRES_DB=reelsnack -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres:15
     ```

3. **Configure Application**
   - Copy `.env.example` to `.env` and update the environment variables
   - Configure database connection in `application.yml` if not using default values

4. **Build and Run**
   ```bash
   # Build the project
   mvn clean install
   
   # Run the application
   mvn spring-boot:run
   ```

5. **Access the Application**
   - API Documentation: http://localhost:8080/swagger-ui.html
   - Actuator Endpoints: http://localhost:8080/actuator

## 🔍 API Documentation

Interactive API documentation is available at `/swagger-ui.html` when the application is running. The OpenAPI specification is available at `/v3/api-docs`.

### Authentication

All secured endpoints require a JWT token in the `Authorization` header:
```
Authorization: Bearer <your-jwt-token>
```

### Available Endpoints

#### Authentication
- `POST /api/v1/auth/signin` - Authenticate user and get JWT token
- `POST /api/v1/auth/signup` - Register a new user
- `POST /api/v1/auth/refresh-token` - Refresh JWT token

#### Test Endpoints
- `GET /api/v1/test/public` - Public endpoint (no auth required)
- `GET /api/v1/test/user` - Requires USER or ADMIN role
- `GET /api/v1/test/admin` - Requires ADMIN role

## 🧪 Testing

Run the test suite with:
```bash
mvn test
```

## 🐳 Docker Support

Build and run the application using Docker:

```bash
# Build the Docker image
docker build -t reelsnack .

# Run the application
docker-compose up -d
```

## 📦 Environment Variables

Key environment variables that can be configured:

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_URL` | Database URL | `******************************************` |
| `DB_USERNAME` | Database username | `postgres` |
| `DB_PASSWORD` | Database password | `postgres` |
| `JWT_SECRET` | Secret key for JWT token generation | Random UUID |
| `JWT_EXPIRATION_MS` | JWT token expiration time in milliseconds | `86400000` (24 hours) |

## 🤝 Contributing

Contributions are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ✉️ Contact

For any questions or feedback, please contact <NAME_EMAIL>

---

<div align="center">
  Made with ❤️ by the ReelSnack Team
</div>
   ```bash
   mvn spring-boot:run
   ```

   The application will be available at `http://localhost:8080/api/v1`

## API Documentation

Once the application is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8080/api/v1/swagger-ui.html`
- OpenAPI JSON: `http://localhost:8080/api/v1/v3/api-docs`

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
JWT_SECRET=your-256-bit-secret-key-32-characters-long
```

## Running with Docker

1. Build the Docker image:
   ```bash
   docker build -t reelsnack .
   ```

2. Run the container:
   ```bash
   docker run -p 8080:8080 --env-file .env reelsnack
   ```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
