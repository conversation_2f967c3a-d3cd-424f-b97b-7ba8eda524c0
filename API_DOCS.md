# ReelSnack API Documentation

## Table of Contents
- [Authentication](#authentication)
- [User Profile Management](#user-profile-management)
  - [Get Current User Profile](#get-current-user-profile)
  - [Update Profile](#update-profile)
  - [Change Password](#change-password)

## Authentication

All endpoints except the authentication endpoints require a valid JWT token in the `Authorization` header.

## User Profile Management

### Get Current User Profile

Retrieves the profile information of the currently authenticated user.

**Endpoint:** `GET /api/v1/profile`

**Headers:**
- `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+1234567890",
  "profilePicture": "https://example.com/profile.jpg",
  "bio": "Food enthusiast and home cook",
  "role": "ROLE_USER",
  "createdAt": "2023-06-21T10:00:00Z",
  "updatedAt": "2023-06-21T11:30:00Z",
  "preferences": {
    "id": 1,
    "emailNotifications": true,
    "pushNotifications": true,
    "smsNotifications": false,
    "language": "en",
    "theme": "light",
    "dietaryRestrictions": ["VEGETARIAN", "GLUTEN_FREE"],
    "preferredCuisines": ["ITALIAN", "MEXICAN"]
  }
}
```

### Update Profile

Updates the profile information of the currently authenticated user.

**Endpoint:** `PUT /api/v1/profile`

**Headers:**
- `Authorization: Bearer <token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+1234567890",
  "profilePicture": "https://example.com/profile.jpg",
  "bio": "Food enthusiast and home cook"
}
```

**Response:**
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+1234567890",
  "profilePicture": "https://example.com/profile.jpg",
  "bio": "Food enthusiast and home cook",
  "role": "ROLE_USER",
  "createdAt": "2023-06-21T10:00:00Z",
  "updatedAt": "2023-06-21T11:30:00Z",
  "preferences": {
    "id": 1,
    "emailNotifications": true,
    "pushNotifications": true,
    "smsNotifications": false,
    "language": "en",
    "theme": "light"
  }
}
```

### Change Password

Changes the password of the currently authenticated user.

**Endpoint:** `POST /api/v1/profile/change-password`

**Headers:**
- `Authorization: Bearer <token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "currentPassword": "oldPassword123",
  "newPassword": "newSecurePassword456",
  "confirmNewPassword": "newSecurePassword456"
}
```

**Response:**
```json
{
  "message": "Password updated successfully"
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "timestamp": "2023-06-21T12:00:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Current password is incorrect",
  "path": "/api/v1/profile/change-password"
}
```

#### 401 Unauthorized
```json
{
  "timestamp": "2023-06-21T12:00:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Full authentication is required to access this resource",
  "path": "/api/v1/profile"
}
```

#### 404 Not Found
```json
{
  "timestamp": "2023-06-21T12:00:00Z",
  "status": 404,
  "error": "Not Found",
  "message": "User not found with id: 999",
  "path": "/api/v1/profile"
}
```
